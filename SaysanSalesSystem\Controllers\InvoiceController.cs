﻿using SaysanSalesSystem.Data;
using SaysanSalesSystem.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace SaysanSalesSystem.Controllers
{
    public class InvoiceController : Controller
    {
        TrocSalesDBEntities db = new TrocSalesDBEntities();
        public ActionResult Index()
        {
            List<SelectListItem> SalesConsultants = (from k in db.SalesConsultants
                                                     select new SelectListItem
                                                     {
                                                         Text = k.DEFINITION_,
                                                         Value = k.Code.ToString()
                                                     }).ToList();

            ViewBag.SalesConsultants = SalesConsultants;
            return View();
        }

        public ActionResult SalesOrder()
        {
            ViewBag.BreadcrumbTitle = "Satış Siparişi Oluştur";
            ViewBag.ShowBreadcrumb = true;
            List<SelectListItem> SalesConsultants = (from k in db.SalesConsultants
                                                     select new SelectListItem
                                                     {
                                                         Text = k.DEFINITION_,
                                                         Value = k.Code.ToString()
                                                     }).ToList();
            ViewBag.SalesConsultants = SalesConsultants;

            List<SelectListItem> Clients = (from k in db.CLCARD
                                            select new SelectListItem
                                            {
                                                Text = k.CustomerTitle,
                                                Value = k.Id.ToString()
                                            }).ToList();
            ViewBag.Clients = Clients;

            var ficheNo = db.INVOICENO.FirstOrDefault();
            ViewBag.FicheNo = ficheNo.FICHENO;

            List<SelectListItem> Payment = (from k in db.DefinitionCollections
                                            select new SelectListItem
                                            {
                                                Text = k.Name,
                                                Value = k.LogicalRef.ToString()
                                            }).ToList();
            ViewBag.Payment = Payment;


            return View();
        }
        public ActionResult InvoicedReceipt()
        {
            ViewBag.BreadcrumbTitle = "Fişli Fatura Oluştur";
            ViewBag.ShowBreadcrumb = true;
            List<SelectListItem> SalesConsultants = (from k in db.SalesConsultants
                                                     select new SelectListItem
                                                     {
                                                         Text = k.DEFINITION_,
                                                         Value = k.Code.ToString()
                                                     }).ToList();
            ViewBag.SalesConsultants = SalesConsultants;

            List<SelectListItem> Clients = (from k in db.CLCARD
                                            select new SelectListItem
                                            {
                                                Text = k.CustomerTitle,
                                                Value = k.Id.ToString()
                                            }).ToList();
            ViewBag.Clients = Clients;

            var ficheNo = db.INVOICENO.FirstOrDefault();
            ViewBag.FicheNo = ficheNo.FICHENO;

            List<SelectListItem> Payment = (from k in db.DefinitionCollections
                                            select new SelectListItem
                                            {
                                                Text = k.Name,
                                                Value = k.LogicalRef.ToString()
                                            }).ToList();
            ViewBag.Payment = Payment;


            return View();
        }
        public ActionResult WholesaleInvoice()
        {
            ViewBag.BreadcrumbTitle = "Toptan Satış Faturası Oluştur";
            ViewBag.ShowBreadcrumb = true;
            List<SelectListItem> SalesConsultants = (from k in db.SalesConsultants
                                                     select new SelectListItem
                                                     {
                                                         Text = k.DEFINITION_,
                                                         Value = k.Code.ToString()
                                                     }).ToList();
            ViewBag.SalesConsultants = SalesConsultants;

            List<SelectListItem> Clients = (from k in db.CLCARD
                                            select new SelectListItem
                                            {
                                                Text = k.CustomerTitle,
                                                Value = k.Id.ToString()
                                            }).ToList();
            ViewBag.Clients = Clients;

            var ficheNo = db.INVOICENO.FirstOrDefault();
            ViewBag.FicheNo = ficheNo.FICHENO;

            List<SelectListItem> Payment = (from k in db.DefinitionCollections
                                            select new SelectListItem
                                            {
                                                Text = k.Name,
                                                Value = k.LogicalRef.ToString()
                                            }).ToList();
            ViewBag.Payment = Payment;


            return View();
        }
        public ActionResult DispatchNote()
        {
            ViewBag.BreadcrumbTitle = "Satış İrsaliyesi Oluştur";
            ViewBag.ShowBreadcrumb = true;
            List<SelectListItem> SalesConsultants = (from k in db.SalesConsultants
                                                     select new SelectListItem
                                                     {
                                                         Text = k.DEFINITION_,
                                                         Value = k.Code.ToString()
                                                     }).ToList();
            ViewBag.SalesConsultants = SalesConsultants;

            List<SelectListItem> Clients = (from k in db.CLCARD
                                            select new SelectListItem
                                            {
                                                Text = k.CustomerTitle,
                                                Value = k.Id.ToString()
                                            }).ToList();
            ViewBag.Clients = Clients;

            var ficheNo = db.INVOICENO.FirstOrDefault();
            ViewBag.FicheNo = ficheNo.FICHENO;

            List<SelectListItem> Payment = (from k in db.DefinitionCollections
                                            select new SelectListItem
                                            {
                                                Text = k.Name,
                                                Value = k.LogicalRef.ToString()
                                            }).ToList();
            ViewBag.Payment = Payment;


            return View();
        }

        [HttpGet]
        public ActionResult FindProductByBarcode(string barcode)
        {
            var data = db.Items.FirstOrDefault(x => x.Barcode == barcode);
            return Json(new { success = true, data = data }, JsonRequestBehavior.AllowGet);
        }
        [HttpPost]
        public ActionResult GetSelectedItems(LogicalRefRequest request)
        {
            var items = db.Items
                .Where(x => request.LogicalRefs.Contains((int)x.LogicalRef))
                .ToList();

            return Json(new { success = true, data = items });
        }

        public class LogicalRefRequest
        {
            public List<int> LogicalRefs { get; set; }
        }

        [HttpPost]
        public async Task<ActionResult> SaveInvoice(SaveInvoiceRequest request)
        {
            try
            {
                var cookie = Request.Cookies["SaysanUser"];
                var name = cookie.Values["Name"];
                var no = cookie.Values["No"];



                // Validation
                if (request == null || !request.Products.Any())
                {
                    return Json(new { success = false, message = "Geçersiz veri." });
                }
                var invoiceNumber = db.INVOICENO.FirstOrDefault().FICHENO;
                var invoice = new Invoice
                {
                    BranchNumber = Convert.ToInt32(no),
                    PaymentRef = request.Payment,
                    CustomerId = request.ClientId,
                    Date = DateTime.Now,
                    CreateDate = DateTime.Now,
                    InvoiceNumber = invoiceNumber,
                    InvoiceType = request.InvoiceType,
                    IsDeleted = false,
                    IsTransfer = 0,
                    PreparedRef = request.Salesman,
                    SalesmanRef = request.SalesConsultantId,
                    WarehouseNumber = Convert.ToInt32(no),
                    CreatedBy = request.Salesman.ToString(),
                    DocumentType = (short?)(request.InvoiceType == 4 ? 0 : 1)



                };
                db.Invoice.Add(invoice);
                db.SaveChanges();
                foreach (var item in request.Products)
                {
                    db.InvoiceLine.Add(new InvoiceLine
                    {
                        Amount = item.Quantity,
                        InvoiceId = invoice.Id,
                        IsDeleted = false,
                        ItemRef = item.LogicalRef,

                    });
                }
                db.SaveChanges();



                return Json(new
                {
                    success = true,
                    message = "Sipariş başarıyla kaydedildi.",
                    invoiceId = invoice.Id
                });
            }
            catch (Exception ex)
            {


                return Json(new
                {
                    success = false,
                    message = "Kaydetme sırasında hata oluştu."
                });
            }
        }

        #region Bilgi Fişi

        public ActionResult dummyattach(int invoiceId)
        {
            var invoice = db.Invoice.FirstOrDefault(x => x.Id == invoiceId);
            var client = db.CLCARD.FirstOrDefault(x => x.Id == invoice.CustomerId);
            //var invoiceLine = db.InvoiceLine.Where(x => x.InvoiceId == invoice.Id).ToList();
            var invoiceType = db.DefintionInvoiceTypes.FirstOrDefault(x => x.Id == invoice.InvoiceType);
            var cookie = Request.Cookies["SaysanUser"];
            var name = cookie.Values["Name"];
            var no = cookie.Values["No"];

            // Tüm invoiceLine'ları getir (InvoiceId'ye göre)
            var invoiceLines = db.InvoiceLine
                .Where(x => x.InvoiceId == invoice.Id && x.IsDeleted != true)
                .ToList();

            // İlgili ürünlerin Id'lerini al
            var itemIds = invoiceLines
                .Select(x => x.ItemRef.Value)
                .Distinct()
                .ToList();

            // Item bilgilerini çek
            var items = db.Items
                .Where(x => itemIds.Contains((int)x.LogicalRef) && x.IsDeleted != true)
                .ToList();

            // Join ve dönüştürme
            var invoiceItems = invoiceLines
                .Select(line =>
                {
                    var item = items.FirstOrDefault(i => i.LogicalRef == line.ItemRef);
                    if (item == null) return null;

                    var quantity = line.Amount ?? 0;
                    var unitPrice = (decimal)(item.ListPrice ?? 0);
                    var discount = 0m; // eğer varsa kendi alanından al

                    return new InvoiceItem
                    {
                        Code = item.Code,
                        Description = item.Name,
                        Quantity = quantity,
                        Unit = "AD", // sabit veya item'dan gelecekse ayarla
                        UnitPrice = unitPrice,
                        Discount = discount,
                        Total = quantity * unitPrice - discount
                    };
                })
                .Where(x => x != null)
                .ToList();

            var preTotal = invoiceItems.Sum(x => x.Quantity * x.UnitPrice);
            var discountTotal = invoiceItems.Sum(x => x.Discount);
            var withoutVAT = preTotal - discountTotal;

            // Eğer her üründe farklı KDV varsa item.Vat üzerinden hesaplanmalı
            // Aksi takdirde sabit bir oran kullan (örnek: %20 = 0.20m)
            var vatTotal = invoiceItems.Sum(x =>
            {
                var lineTotal = (x.Quantity * x.UnitPrice) - x.Discount;
                var vatRate = 0.20m; // Sabit KDV oranı, istersen item'dan al
                return lineTotal * vatRate;
            });

            var generalTotal = withoutVAT + vatTotal;

            var summary = new InvoiceSummary
            {
                PreTotal = Math.Round(preTotal, 2),
                DiscountTotal = Math.Round(discountTotal, 2),
                WithoutVAT = Math.Round(withoutVAT, 2),
                VATTotal = Math.Round(vatTotal, 2),
                GeneralTotal = Math.Round(generalTotal, 2),
                AmountInWords = NumberToText(generalTotal)
            };


            var model = new InvoiceViewModel
            {
                Account = new AccountInfo
                {
                    Code = client.CustomerCode,
                    Title = client.CustomerTitle,
                    Address = client.Adress,
                    TaxOffice = client.City,
                    TaxNumber = client.TaxNo
                },
                Invoice = new InvoiceInfo
                {
                    No = invoice.InvoiceNumber,
                    Date = invoice.Date.Value.ToString("dd.MM.yyyy"),
                    Type = invoiceType.Name,
                    Warehouse = no + " / " +name
                },
                Items = invoiceItems,
                Summary = summary
            };

            return AttachInfoSlipToHtml(model);
        }

        public ActionResult AttachInfoSlipToHtml(InvoiceViewModel model)
        {
            var path = Server.MapPath("~/Content/Document/InfoSlip.html");
            var html = System.IO.File.ReadAllText(path, Encoding.UTF8);

            html = html
                .Replace("{{AccountCode}}", model.Account.Code)
                .Replace("{{AccountTitle}}", model.Account.Title)
                .Replace("{{AccountAddress}}", model.Account.Address)
                .Replace("{{AccountTaxOffice}}", model.Account.TaxOffice)
                .Replace("{{AccountTaxNumber}}", model.Account.TaxNumber)

                .Replace("{{InvoiceNo}}", model.Invoice.No)
                .Replace("{{InvoiceDate}}", model.Invoice.Date)
                .Replace("{{InvoiceType}}", model.Invoice.Type)
                .Replace("{{InvoiceWarehouse}}", model.Invoice.Warehouse)

                .Replace("{{PreTotal}}", model.Summary.PreTotal.ToString("N2"))
                .Replace("{{VATTotal}}", model.Summary.VATTotal.ToString("N2"))
                .Replace("{{WithoutVAT}}", model.Summary.WithoutVAT.ToString("N2"))
                .Replace("{{DiscountTotal}}", model.Summary.DiscountTotal.ToString("N2"))
                .Replace("{{GeneralTotal}}", model.Summary.GeneralTotal.ToString("N2"))

                .Replace("{{AmountInWords}}", model.Summary.AmountInWords);


            var sb = new StringBuilder();
            foreach (var item in model.Items)
            {
                sb.Append("<tr>");
                sb.AppendFormat("<td>{0}</td>", item.Code);
                sb.AppendFormat("<td>{0}</td>", item.Description);
                sb.AppendFormat("<td style=\"text-align:right;\">{0}</td>", item.Quantity);
                sb.AppendFormat("<td style=\"text-align:center;\">{0}</td>", item.Unit);
                sb.AppendFormat("<td style=\"text-align:right;\">{0:N2}</td>", item.UnitPrice);
                sb.AppendFormat("<td style=\"text-align:right;\">{0:N2}</td>", item.Discount);
                sb.AppendFormat("<td style=\"text-align:right;\">{0:N2}</td>", item.Total);
                sb.Append("</tr>");
            }
            html = html.Replace("{{ItemRows}}", sb.ToString());

            return Content(html, "text/html");

        }

        private string NumberToText(decimal amount)
        {
            long lira = (long)Math.Floor(amount);
            int kurus = (int)Math.Round((amount - lira) * 100);
            string[] units = { "", "bir", "iki", "üç", "dört", "beş", "altı", "yedi", "sekiz", "dokuz" };
            string[] tens = { "", "on", "yirmi", "otuz", "kırk", "elli", "altmış", "yetmiş", "seksen", "doksan" };
            string[] scales = { "", "bin", "milyon", "milyar", "trilyon" };

            Func<int, string> threeDigits = n =>
            {
                string s = "";
                int h = n / 100;
                int rem = n % 100;
                if (h > 0)
                    s += h > 1 ? units[h] + " yüz" : " yüz";
                int t = rem / 10;
                int o = rem % 10;
                if (t > 0)
                    s += (s.Length > 0 ? " " : "") + tens[t];
                if (o > 0)
                    s += (s.Length > 0 ? " " : "") + units[o];
                return s;
            };

            var parts = new List<string>();
            long num = lira;
            int scaleIndex = 0;
            while (num > 0)
            {
                int group = (int)(num % 1000);
                if (group > 0)
                {
                    string txt = threeDigits(group);
                    if (scaleIndex == 1 && group == 1)
                        txt = "bin";
                    else if (scaleIndex > 1)
                        txt += " " + scales[scaleIndex];
                    parts.Insert(0, txt);
                }
                num /= 1000;
                scaleIndex++;
            }

            string result = (parts.Count > 0 ? string.Join(" ", parts) : "sıfır") + " lira";
            if (kurus > 0)
                result += " " + threeDigits(kurus) + " kuruş";
            return result;
        }

        #region Bilgi Fişi Model
        public class InvoiceViewModel
        {
            public AccountInfo Account { get; set; }
            public InvoiceInfo Invoice { get; set; }
            public List<InvoiceItem> Items { get; set; }
            public InvoiceSummary Summary { get; set; }
        }

        public class AccountInfo
        {
            public string Code { get; set; }
            public string Title { get; set; }
            public string Address { get; set; }
            public string TaxOffice { get; set; }
            public string TaxNumber { get; set; }
        }

        public class InvoiceInfo
        {
            public string No { get; set; }
            public string Date { get; set; }
            public string Type { get; set; }
            public string Warehouse { get; set; }
        }

        public class InvoiceItem
        {
            public string Code { get; set; }
            public string Description { get; set; }
            public decimal Quantity { get; set; }
            public string Unit { get; set; }
            public decimal UnitPrice { get; set; }
            public decimal Discount { get; set; }
            public decimal Total { get; set; }
        }

        public class InvoiceSummary
        {
            public decimal PreTotal { get; set; }
            public decimal DiscountTotal { get; set; }
            public decimal WithoutVAT { get; set; }
            public decimal VATTotal { get; set; }
            public decimal GeneralTotal { get; set; }
            public string AmountInWords { get; set; }
        }

        #endregion

        #endregion

        #region Ambar Fişi
        public ActionResult dummyattachwarehouse()
        {
            var model = new WareHouseTransferViewModel
            {
                Transfer = new TransferInfo
                {
                    TransferDate = DateTime.Now.ToString("dd.MM.yyyy"),
                    TransferDocNumber = "Trc0000028464",

                    ToWarehouseName = "Merkez",
                    ToWarehouseAddress = "adsasdasdsa",
                    ToWarehousePhone = "0941205",

                    FromWarehouseName = "Organize",
                    FromWarehouseAddress = "sdaasdsad",
                    FromWarehousePhone = "asdsadasdas",
                },

                Items = new List<InvoiceItem>
                {
                    new InvoiceItem { Code = "PRF-20X40", Description = "20x40 Alüminyum Profil (2m)", Quantity = 10, Unit = "AD"},
                    new InvoiceItem { Code = "STL-PLT-3MM", Description = "3 mm Çelik Sac Levha (1x2m)", Quantity = 5, Unit = "PKT"},
                    new InvoiceItem { Code = "NUT-M6", Description = "M6 Somun (1000 adet)", Quantity = 1, Unit = "KOLİ"}
                },
            };

            return AttachWareHouseTransferSlipToHtml(model);
        }

        public ActionResult AttachWareHouseTransferSlipToHtml(WareHouseTransferViewModel model)
        {
            var path = Server.MapPath("~/Content/Document/WareHouseTransferSlip.html");
            var html = System.IO.File.ReadAllText(path, Encoding.UTF8);

            html = html
                .Replace("{{DocumentNo}}", model.Transfer.TransferDocNumber)
                .Replace("{{DocumentDate}}", model.Transfer.TransferDate)

                .Replace("{{ToWarehouseName}}", model.Transfer.ToWarehouseName)
                .Replace("{{ToWarehouseAddress}}", model.Transfer.ToWarehouseAddress)
                .Replace("{{ToWarehousePhone}}", model.Transfer.ToWarehousePhone)


                .Replace("{{FromWarehouseName}}", model.Transfer.FromWarehouseName)
                .Replace("{{FromWarehouseAddress}}", model.Transfer.FromWarehouseAddress)
                .Replace("{{FromWarehousePhone}}", model.Transfer.FromWarehousePhone)

                ;


            var sb = new StringBuilder();
            foreach (var item in model.Items)
            {
                sb.Append("<tr>");
                sb.AppendFormat("<td>{0}</td>", item.Code);
                sb.AppendFormat("<td>{0}</td>", item.Description);
                sb.AppendFormat("<td>{0}</td>", item.Quantity);
                sb.AppendFormat("<td>{0}</td>", item.Unit);
                sb.Append("</tr>");
            }
            html = html.Replace("{{ItemRows}}", sb.ToString());

            return Content(html, "text/html");
        }

        #region Ambar Fişi Model
        public class WareHouseTransferViewModel
        {
            public TransferInfo Transfer { get; set; }
            public List<InvoiceItem> Items { get; set; }
        }

        public class TransferInfo
        {
            public string TransferDate { get; set; }
            public string TransferDocNumber { get; set; }

            public string ToWarehouseName { get; set; }
            public string ToWarehouseAddress { get; set; }
            public string ToWarehousePhone { get; set; }

            public string FromWarehouseName { get; set; }
            public string FromWarehouseAddress { get; set; }
            public string FromWarehousePhone { get; set; }
        }
        #endregion
        #endregion

    }
}