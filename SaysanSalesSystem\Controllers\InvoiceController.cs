﻿using SaysanSalesSystem.Data;
using SaysanSalesSystem.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace SaysanSalesSystem.Controllers
{
    public class InvoiceController : Controller
    {
        TrocSalesDBEntities db = new TrocSalesDBEntities();
        public ActionResult Index()
        {
            List<SelectListItem> SalesConsultants = (from k in db.SalesConsultants
                                                        select new SelectListItem
                                                        {
                                                            Text = k.Title,
                                                            Value = k.Id.ToString()
                                                        }).ToList();

            ViewBag.SalesConsultants = SalesConsultants;
            return View();
        }

        public ActionResult SalesOrder()
        {
            ViewBag.BreadcrumbTitle = "Satış Siparişi Oluştur";
            ViewBag.ShowBreadcrumb = true;
            List<SelectListItem> SalesConsultants = (from k in db.SalesConsultants
                                                    select new SelectListItem
                                                    {
                                                        Text = k.Title,
                                                        Value = k.Id.ToString()
                                                    }).ToList();
            ViewBag.SalesConsultants = SalesConsultants;

            List<SelectListItem> Clients = (from k in db.CLCARD
                                                     select new SelectListItem
                                                     {
                                                         Text = k.CustomerTitle,
                                                         Value = k.Id.ToString()
                                                     }).ToList();
            ViewBag.Clients = Clients;

            var ficheNo = db.INVOICENO.FirstOrDefault();
            ViewBag.FicheNo = ficheNo.FICHENO;

            List<SelectListItem> Payment = (from k in db.DefinitionCollections
                                            select new SelectListItem
                                            {
                                                Text = k.Name,
                                                Value = k.LogicalRef.ToString()
                                            }).ToList();
            ViewBag.Payment = Payment;


            return View();
        }
        [HttpGet]
        public ActionResult FindProductByBarcode(string barcode)
        {
            var data = db.Items.FirstOrDefault(x => x.Barcode == barcode);
            return Json(new { success = true, data = data }, JsonRequestBehavior.AllowGet);
        }
        [HttpPost]
        public ActionResult GetSelectedItems(LogicalRefRequest request)
        {
            var items = db.Items
                .Where(x => request.LogicalRefs.Contains((int)x.LogicalRef))
                .ToList();

            return Json(new { success = true, data = items });
        }

        public class LogicalRefRequest
        {
            public List<int> LogicalRefs { get; set; }
        }

        [HttpPost]
        public async Task<ActionResult> SaveInvoice(SaveInvoiceRequest request)
        {
            try
            {
                // Validation
                if (request == null || !request.Products.Any())
                {
                    return Json(new { success = false, message = "Geçersiz veri." });
                }

                var invoice = new Invoice
                {
                    BranchNumber = 0,
                    PaymentRef = request.Payment,
                    CustomerId = request.ClientId,
                    Date = DateTime.Now,
                    CreateDate = DateTime.Now,
                    InvoiceNumber = request.InvoiceNumber,
                    InvoiceType = request.InvoiceType,
                    IsDeleted = false,
                    IsTransfer = 0,
                    PreparedRef = request.Salesman,
                    SalesmanRef = request.SalesConsultantId,
                    WarehouseNumber = 0,
                    CreatedBy = request.Salesman.ToString(),
                    DocumentType = 1



                };
                db.Invoice.Add(invoice);
                db.SaveChanges();
                foreach (var item in request.Products)
                {
                    db.InvoiceLine.Add(new InvoiceLine
                    {
                        Amount = item.Quantity,
                        InvoiceId = invoice.Id,
                        IsDeleted = false,
                        ItemRef = item.LogicalRef,

                    });
                }
                db.SaveChanges();



                return Json(new
                {
                    success = true,
                    message = "Sipariş başarıyla kaydedildi.",
                    invoiceId = invoice.Id
                });
            }
            catch (Exception ex)
            {


                return Json(new
                {
                    success = false,
                    message = "Kaydetme sırasında hata oluştu."
                });
            }
        }

    }
}