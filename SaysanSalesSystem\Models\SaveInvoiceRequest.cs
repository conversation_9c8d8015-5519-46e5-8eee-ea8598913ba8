﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SaysanSalesSystem.Models
{
    public class SaveInvoiceRequest
    {
        public int SalesConsultantId { get; set; }
        public string InvoiceNumber { get; set; }
        public int ClientId { get; set; }
        public int Salesman { get; set; }
        public int Payment { get; set; }
        public int InvoiceType { get; set; }
        public List<ProductLineItem> Products { get; set; }
    }

    public class ProductLineItem
    {
        public int ProductId { get; set; }
        public int LogicalRef { get; set; }
        public string Name { get; set; }
        public string Barcode { get; set; }
        public int Quantity { get; set; }
        public string Code { get; set; }
        public decimal Price { get; set; }
    }
}