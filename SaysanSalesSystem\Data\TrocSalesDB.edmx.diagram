<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
 <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <!-- Diagram content (shape and connector positions) -->
    <edmx:Diagrams>
      <Diagram DiagramId="cccc06ff081e4a49b10aa1b70eae9f62" Name="Diagram1">
        <EntityTypeShape EntityType="TrocSalesDBModel.Clients" Width="1.5" PointX="0.75" PointY="0.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.CompanyTypes" Width="1.5" PointX="2.75" PointY="0.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.DefinitionBranches" Width="1.5" PointX="2.75" PointY="3.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.DefinitionCollections" Width="1.5" PointX="4.75" PointY="0.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.DefintionInvoiceTypes" Width="1.5" PointX="4.75" PointY="3.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.InvoiceLine" Width="1.5" PointX="0.75" PointY="6.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.TransferLogs" Width="1.5" PointX="2.75" PointY="6.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.WarehouseTransferItems" Width="1.5" PointX="4.75" PointY="6.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.DefinitionCities" Width="1.5" PointX="6.75" PointY="5.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.CLCARD" Width="1.5" PointX="8.75" PointY="0.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.INVOICENO" Width="1.5" PointX="8.75" PointY="6.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.MainGroups" Width="1.5" PointX="8.75" PointY="9.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.SubGroups" Width="1.5" PointX="0.75" PointY="10.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.WareHouse" Width="1.5" PointX="2.75" PointY="10.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.Invoice" Width="1.5" PointX="6.75" PointY="0.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.ProductPhotos" Width="1.5" PointX="10.75" PointY="2.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.SalesConsultants" Width="1.5" PointX="10.625" PointY="0.75" />
        <EntityTypeShape EntityType="TrocSalesDBModel.Items" Width="1.5" PointX="5.375" PointY="10.75" />
      </Diagram>
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>