//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace SaysanSalesSystem.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class Items
    {
        public int Id { get; set; }
        public Nullable<int> LogicalRef { get; set; }
        public Nullable<short> Active { get; set; }
        public Nullable<short> CardType { get; set; }
        public string Code { get; set; }
        public string Barcode { get; set; }
        public string Name { get; set; }
        public string MainGroup { get; set; }
        public string SubGroup { get; set; }
        public string StgrpCode { get; set; }
        public string ProducerCode { get; set; }
        public string SpeCode { get; set; }
        public string CyphCode { get; set; }
        public Nullable<double> Vat { get; set; }
        public Nullable<double> ListPrice { get; set; }
        public Nullable<int> UnitSetRef { get; set; }
        public string ImagePath { get; set; }
        public Nullable<int> ModifiedById { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<int> CreatedById { get; set; }
        public Nullable<System.DateTime> CreatedDate { get; set; }
        public Nullable<bool> IsDeleted { get; set; }
        public Nullable<int> SpeCodeLogicalRef { get; set; }
    }
}
