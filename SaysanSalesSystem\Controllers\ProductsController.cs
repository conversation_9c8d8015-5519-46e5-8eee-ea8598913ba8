﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using SaysanSalesSystem.Data;
using SaysanSalesSystem.Models;

namespace SaysanSalesSystem.Controllers
{

    public class ProductsController : Controller
    {
        TrocSalesDBEntities db = new TrocSalesDBEntities();

        // GET: Products
        public ActionResult Index()
        {

            return View();
        }

        public ActionResult ProductPhotos()
        {
            ViewBag.BreadcrumbTitle = "Ürün Fotoğrafı Ekle";
            ViewBag.ShowBreadcrumb = true;

            return View();
        }

        public ActionResult MainGroupList()
        {
            var list = db.MainGroups.ToList();

            ViewBag.List = list;

            return PartialView();
        }

        public ActionResult SubGroupList(string MainGroup)
        {
            var list = db.SubGroups.Where(x => x.MainGroup == MainGroup
            ).ToList();

            ViewBag.List = list;

            return PartialView();
        }

        public ActionResult ItemList(string MainGroup, string SubGroup)
        {
            var list = db.Items.Where(x => x.MainGroup == MainGroup && x.SubGroup == SubGroup && x.IsDeleted == false).ToList();

            ViewBag.List = list;

            return PartialView();
        }

        [HttpPost]
        public JsonResult UploadPhotos(int itemId, List<HttpPostedFileBase> photos)
        {
            try
            {
                List<string> savedPhotoPaths = new List<string>();

                foreach (var photo in photos)
                {
                    if (photo != null && photo.ContentLength > 0)
                    {
                        string fileName = Guid.NewGuid().ToString() + Path.GetExtension(photo.FileName);
                        string folderPath = Server.MapPath("~/Uploads/");
                        string fullPath = Path.Combine(folderPath, fileName);

                        if (!Directory.Exists(folderPath))
                        {
                            Directory.CreateDirectory(folderPath);
                        }

                        photo.SaveAs(fullPath);

                        string relativePath = "/Uploads/" + fileName;
                        savedPhotoPaths.Add(relativePath);

                        ProductPhotos record = new ProductPhotos();
                        record.Type = "Ürün";
                        record.ItemId = itemId;
                        record.ImageName = photo.FileName;
                        record.ImagePath = relativePath;
                        record.CreatedDate = DateTime.Now;
                        record.IsDeleted = false;

                        db.ProductPhotos.Add(record);
                    }
                }

                // eski fotoğrafı sil
                var lastPhoto = db.ProductPhotos.Where(x => x.ItemId == itemId && x.IsDeleted == false).FirstOrDefault();

                if (lastPhoto != null)
                {
                    lastPhoto.ModifiedDate = DateTime.Now;
                    lastPhoto.IsDeleted = true;
                    if (System.IO.File.Exists(lastPhoto.ImagePath))
                    {
                        System.IO.File.Delete(lastPhoto.ImagePath);
                    }
                }


                var result = db.SaveChanges();

                if (result >= 1)
                {
                    return Json(new { success = true, message = "İşlem Başarılı" }, JsonRequestBehavior.AllowGet);
                }
                else
                {
                    return Json(new { success = false, message = "İşlem Başarısız" }, JsonRequestBehavior.AllowGet);
                }

            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Bir Hata Oluştu!" }, JsonRequestBehavior.AllowGet);
            }

        }

        public JsonResult GetItemPhotos(int itemId)
        {
            var photos = db.ProductPhotos
                           .Where(p => p.ItemId == itemId && p.IsDeleted == false)
                           .Select(p => new
                           {
                               p.Id,
                               p.ImagePath,
                               p.ImageName
                           }).ToList();

            return Json(photos, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public JsonResult DeleteItemPhoto(int id)
        {
            try
            {
                var photo = db.ProductPhotos.Where(x=>x.ItemId == id && x.IsDeleted == false).FirstOrDefault();
                if (photo == null)
                {
                    return Json(new { success = false, message = "Fotoğraf bulunamadı." });
                }

                var fullPath = Server.MapPath(photo.ImagePath);
                if (System.IO.File.Exists(fullPath))
                {
                    System.IO.File.Delete(fullPath);
                }


                photo.IsDeleted = true;
                //photo.ModifiedDate = DateTime.Now;
                var result = db.SaveChanges();

                if (result >= 1)
                {
                    return Json(new { success = true, message = "İşlem Başarılı" }, JsonRequestBehavior.AllowGet);
                }
                else
                {
                    return Json(new { success = false, message = "İşlem Başarısız" }, JsonRequestBehavior.AllowGet);
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Bir hata oluştu." });
            }
        }

    }
}