﻿@{
    ViewBag.Title = "Müşteriler";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<style>
    .container-fluid {
        padding: 20px;
    }

    .invoice-form-wide {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        padding: 25px;
        margin-bottom: 20px;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f1f5f9;
    }

    .page-title {
        color: #1e293b;
        font-size: 28px;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 12px;
    }

        .page-title i {
            color: #4f46e5;
            font-size: 24px;
        }

    /* <PERSON><PERSON> */
    .custom-search-container {
        display: flex;
        justify-content: center;
        margin-bottom: 25px;
    }

    .custom-search-wrapper {
        position: relative;
        width: 100%;
        max-width: 500px;
    }

    .custom-search-input {
        width: 100%;
        padding: 15px 50px 15px 20px;
        border: 2px solid #e2e8f0;
        border-radius: 25px;
        font-size: 16px;
        transition: all 0.3s ease;
        background: #fafafa;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

        .custom-search-input:focus {
            outline: none;
            border-color: #4f46e5;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
        }

    .custom-search-icon {
        position: absolute;
        right: 18px;
        top: 50%;
        transform: translateY(-50%);
        color: #64748b;
        font-size: 18px;
        pointer-events: none;
    }

    .search-loading {
        position: absolute;
        right: 18px;
        top: 50%;
        transform: translateY(-50%);
        display: none;
    }

    /* Genişletilmiş Müşteri Ekle Butonu */
    .btn-add-customer {
        background: linear-gradient(135deg, #4f46e5, #7c3aed);
        border: none;
        padding: 12px 30px;
        border-radius: 12px;
        color: white;
        font-weight: 600;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 10px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        min-width: 180px;
        justify-content: center;
    }

        .btn-add-customer:hover {
            background: linear-gradient(135deg, #4338ca, #6d28d9);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
            color: white;
        }

        .btn-add-customer:active {
            transform: translateY(0);
        }

    /* DataTable Özelleştirmeleri */
    .dataTables_wrapper .dataTables_filter {
        display: none !important; /* Varsayılan arama kutusunu gizle */
    }

    .dataTables_wrapper .dataTables_length {
        margin-bottom: 15px;
    }

        .dataTables_wrapper .dataTables_length select {
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            padding: 5px 10px;
        }

    .dataTables_wrapper .dataTables_info {
        color: #64748b;
        font-weight: 500;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        border-radius: 8px !important;
        margin: 0 2px;
        border: none !important;
    }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: #4f46e5 !important;
            color: white !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: #f1f5f9 !important;
            color: #4f46e5 !important;
        }

    /* Tablo Stilleri */
    .table {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

        .table thead th {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            color: #1e293b;
            font-weight: 600;
            border: none;
            padding: 15px 12px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table tbody td {
            padding: 12px;
            vertical-align: middle;
            border-color: #f1f5f9;
        }

        .table tbody tr:hover {
            background-color: #f8fafc;
        }

    /* Loading Animasyonu */
    .dataTables_processing {
        background: rgba(79, 70, 229, 0.1) !important;
        color: #4f46e5 !important;
        border: 2px solid #4f46e5 !important;
        border-radius: 8px !important;
        font-weight: 600 !important;
    }

    /* Responsive */
    @@media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 15px;
            align-items: stretch;
        }

        .btn-add-customer {
            width: 100%;
        }

        .custom-search-wrapper {
            max-width: 100%;
        }
    }

    /* Arama sonucu vurgulama */
    .search-highlight {
        background-color: #fef3c7;
        padding: 2px 4px;
        border-radius: 4px;
        font-weight: 600;
    }
</style>
<div class="container-fluid px-0">
    <div class="invoice-form-wide">
        <!-- Sayfa Başlığı ve Müşteri Ekle Butonu -->
        <div class="page-header">
            <h2 class="page-title">
                <i class="fas fa-users"></i>
                Müşteriler
            </h2>

            <!-- Önce arama kutusu -->
            <div class="custom-search-wrapper">
                <input type="text"
                       class="custom-search-input"
                       id="customSearch"
                       placeholder="Müşteri ara... (En az 3 karakter girin)">
                <i class="fas fa-search custom-search-icon" id="searchIcon"></i>
                <div class="search-loading" id="searchLoading">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
            </div>

            <!-- Sonra buton -->
            <button class="btn btn-add-customer" id="addClientBtn">
                <i class="fas fa-plus"></i>
                Müşteri Ekle
            </button>
        </div>



        <!-- DataTable -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped w-100" id="clientsTable">
                <thead>
                    <tr>
                        <th>Kod</th>
                        <th>Ünvan</th>
                        <th>Adres</th>
                        <th>Şehir</th>
                        <th>Telefon</th>
                        <th>Vergi Dairesi</th>
                        <th>Vergi No</th>
                        <th>TC No</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- DataTable ile dinamik yüklenecek -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Müşteri Ekle Modal -->
<div class="modal fade" id="clientModal" tabindex="-1" aria-labelledby="clientModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #4f46e5, #7c3aed); color: white;">
                <h5 class="modal-title" id="clientModalLabel">
                    <i class="fas fa-user-plus me-2"></i>
                    Yeni Müşteri Ekle
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="padding: 0;">
                <!-- Buraya Create.cshtml partial view'i gelecek -->
                <div style="text-align: center; color: #64748b;">

                    <p>

                        <strong>@Html.Partial("Create")</strong>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>



@section scripts {
    <script>
        $(document).ready(function () {
            // DataTable'ı başlat
            var table = $('#clientsTable').DataTable({
                processing: true,
                serverSide: true, // Server-side processing aktif
                ordering: true,
                searching: false, // Özel arama kullandığınız için false
                deferLoading: 0,
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/tr.json',
                    paginate: {
                        next: '<i class="fa fa-angle-double-right" aria-hidden="true"></i>',
                        previous: '<i class="fa fa-angle-double-left" aria-hidden="true"></i>'
                    },
                    processing: "Yükleniyor...",
                    search: "Ara:",
                    lengthMenu: "Sayfa başına _MENU_ kayıt göster",
                    info: "_TOTAL_ kayıttan _START_ - _END_ arası gösteriliyor",
                    infoEmpty: "Kayıt bulunamadı",
                    infoFiltered: "(_MAX_ kayıt içerisinden filtrelendi)",
                    emptyTable: "Tabloda veri bulunmuyor"
                },

                ajax: {
                    url: '/Client/GetClientsMore',
                    type: 'POST',
                    contentType: 'application/json; charset=utf-8',
                    data: function (d) {
                        // Özel arama değerini ekle
                        d.customSearch = $('#customSearch').val() || '';
                        console.log('Gönderilen veri:', d); // Debug için
                        return JSON.stringify(d);
                    },
                    dataSrc: function (json) {
                        // Arama geçerli değilse tabloyu boş göster
                        if ($('#customSearch').val().length < 3) {
                            json.recordsTotal = 0;
                            json.recordsFiltered = 0;
                            return [];
                        }
                        return json.data;
                    },
                    error: function (xhr, error, thrown) {
                        console.error('DataTable AJAX hatası:', error);
                        console.error('XHR:', xhr);
                        console.error('Response:', xhr.responseText);

                        // Hata durumunda kullanıcıya bildir
                        alert('Veri yüklenirken hata oluştu: ' + error);
                    }
                },
                columns: [
                    {
                        data: 'customerCode',
                        name: 'CustomerCode',
                        orderable: true,
                        searchable: true
                    },
                    {
                        data: 'customerTitle',
                        name: 'CustomerTitle',
                        orderable: true,
                        searchable: true
                    },
                    {
                        data: 'adress',
                        name: 'Adress',
                        orderable: true,
                        searchable: true
                    },
                    {
                        data: 'city',
                        name: 'City',
                        orderable: true,
                        searchable: true
                    },
                    {
                        data: 'telNo',
                        name: 'TelNo',
                        orderable: true,
                        searchable: true
                    },
                    {
                        data: 'taxOffice',
                        name: 'TaxOffice',
                        orderable: true,
                        searchable: true
                    },
                    {
                        data: 'taxNo',
                        name: 'TaxNo',
                        orderable: true,
                        searchable: true
                    },
                    {
                        data: 'tcNo',
                        name: 'TcNo',
                        orderable: true,
                        searchable: true
                    }
                ],

                order: [[0, 'asc']],
                pageLength: 25,
                lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
                scrollX: true,
                drawCallback: function (settings) {
                    $(".dataTables_paginate").addClass('mb-4 mt-2');

                    // Arama sonuçlarını vurgula
                    const searchValue = $('#customSearch').val();
                    if (searchValue && searchValue.length >= 3) {
                        highlightSearchResults(searchValue);
                    }
                },
                initComplete: function (settings, json) {
                    console.log('DataTable yüklendi');
                    console.log('İlk veri:', json);
                }
            });

            // Özel arama fonksiyonu
            let searchTimeout;
            $('#customSearch').on('input', function () {
                const searchValue = $(this).val();

                // Loading göster
                $('#searchIcon').hide();
                $('#searchLoading').show();

                // Önceki timeout'u temizle
                clearTimeout(searchTimeout);

                // 3 karakterden az ise aramayı temizle
                if (searchValue.length < 3 && searchValue.length > 0) {
                    
                    table.ajax.reload(function () {
                        // Loading gizle
                        $('#searchLoading').hide();
                        $('#searchIcon').show();
                    });
                    table.deferLoading = 0;
                    return;
                }

                // 500ms gecikme ile arama yap
                searchTimeout = setTimeout(function () {
                    // DataTable'ı yeniden yükle
                    table.ajax.reload(function () {
                        // Loading gizle
                        $('#searchLoading').hide();
                        $('#searchIcon').show();
                    });
                }, 500);
            });

            // Enter tuşuna basıldığında hemen ara
            $('#customSearch').on('keypress', function (e) {
                if (e.which === 13) { // Enter tuşu
                    clearTimeout(searchTimeout);
                    const searchValue = $(this).val();

                    if (searchValue.length >= 3 || searchValue.length === 0) {
                        $('#searchIcon').hide();
                        $('#searchLoading').show();

                        table.ajax.reload(function () {
                            $('#searchLoading').hide();
                            $('#searchIcon').show();
                        });
                    }
                }
            });

            // Arama sonuçlarını vurgulama fonksiyonu
            function highlightSearchResults(searchTerm) {
                if (!searchTerm) return;

                const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');

                $('#clientsTable tbody tr:visible').each(function () {
                    $(this).find('td').each(function () {
                        const $td = $(this);
                        let content = $td.text(); // HTML yerine text kullan

                        // Önce mevcut vurgulamaları temizle
                        content = content.replace(/<span class="search-highlight">(.*?)<\/span>/gi, '$1');

                        if (content && typeof content === 'string') {
                            const highlighted = content.replace(regex, '<span class="search-highlight">$1</span>');
                            $td.html(highlighted);
                        }
                    });
                });
            }

            // Regex escape fonksiyonu
            function escapeRegExp(string) {
                return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            }

            // Arama kutusunu temizle
            $('#customSearch').on('focus', function () {
                $(this).parent().addClass('focused');
            }).on('blur', function () {
                $(this).parent().removeClass('focused');
            });
        });

        // Modal aç
        document.getElementById('addClientBtn').addEventListener('click', function () {
            var modal = new bootstrap.Modal(document.getElementById('clientModal'));
            modal.show();
        });

        // Müşteri eklendikten sonra tabloyu yenile
        function refreshTable() {
            $('#clientsTable').DataTable().ajax.reload();
        }

        // Sayfa yüklendiğinde arama kutusuna odaklan
        $(window).on('load', function () {
            setTimeout(() => {
                $('#customSearch').focus();
            }, 500);
        });
    </script>

    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" />
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>

    <style>
        .search-highlight {
            background-color: yellow;
            font-weight: bold;
        }
    </style>
}