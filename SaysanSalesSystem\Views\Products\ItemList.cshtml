﻿
<div class="mt-4">
    <input type="text" id="searchInput" class="form-control" style="font-size:16px;" placeholder="Ürün ara..." onkeyup="filterItems()" />

    <div class="row mt-3" id="itemList">
        @foreach (SaysanSalesSystem.Data.Items item in ViewBag.List)
        {
            var hasImage = !string.IsNullOrEmpty(item.ImagePath);
            <div class="col-md-3 col-sm-3 col-lg-3 mb-4" id="<EMAIL>">
                <div class="card h-100 shadow-sm">
                    <div class="position-relative" style="height: 200px;">
                        @if (hasImage)
                        {
                            <img src="@item.ImagePath" class="card-img-top h-100 w-100" style="object-fit: cover;" />
                            <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1" title="Fotoğraf Sil"
                                    onclick="deleteItemPhoto(@item.LogicalRef)">
                                <i class="fas fa-times"></i>
                            </button>
                        }
                        else
                        {
                            <div class="d-flex justify-content-center align-items-center bg-light border-bottom h-100 w-100 text-muted" style="font-size: 14px;">
                                <span>Fotoğraf Yok</span>
                            </div>
                        }
                    </div>

                    <div class="card-body d-flex flex-column justify-content-between">
                        <div>
                            <h5 class="card-title">@item.Name</h5>
                            <p class="card-text text-muted">@item.Barcode</p>
                        </div>
                        <div class="mt-3 d-flex justify-content-between">
                            <button class="btn btn-outline-primary btn-sm me-1" title="Fotoğraf Ekle"
                                    onclick="addPhoto(@item.LogicalRef, '@item.Name.Replace("'", "\\'")','@item.MainGroup.Replace("'", "\\'")','@item.SubGroup.Replace("'", "\\'")')">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>
                    </div>

                    <div id="<EMAIL>" class="mt-2 d-none px-3 pb-2">
                        <div class="photo-gallery d-flex flex-wrap gap-2"></div>
                    </div>
                </div>
            </div>
        }

    </div>
</div>
