﻿/* Modern Select2 CSS */
.modern-select-container {
    position: relative;
    width: 100%;
    /*max-width: 400px;*/
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin-bottom: 15px;
    user-select: none;
}

.modern-select-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 45px;
    padding: 0 15px;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.25s ease;
  /*  box-shadow: 0 2px 5px rgba(0,0,0,0.05);*/
}

.modern-select-container:hover .modern-select-header {
    border-color: #d0d0d0;
    box-shadow: 0 3px 8px rgba(0,0,0,0.08);
}

.modern-select-container.open .modern-select-header {
    border-radius: 8px 8px 0 0;
/*    border-color: #3498db;*/
    box-shadow: 0 4px 10px rgba(52, 152, 219, 0.1);
}

.modern-select-selected {
    color: #333;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 20px);
}

.modern-select-arrow {
    width: 10px;
    height: 10px;
    border-right: 2px solid #999;
    border-bottom: 2px solid #999;
    transform: rotate(45deg);
    transition: transform 0.3s ease;
    margin-left: 10px;
}

.modern-select-container.open .modern-select-arrow {
    transform: rotate(-135deg);
    margin-top: 5px;
}

.modern-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    opacity: 0;
    visibility: hidden;
}

.modern-select-container.open .modern-select-dropdown {
    max-height: 350px;
    opacity: 1;
    visibility: visible;
}

.modern-select-search {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

    .modern-select-search input {
        width: 100%;
        padding: 8px 10px;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        font-size: 14px;
        outline: none;
        transition: border-color 0.2s ease;
    }

        .modern-select-search input:focus {
            border-color: #3498db;
        }

.modern-select-options {
    list-style: none;
    margin: 0;
    padding: 0;
    max-height: 280px;
    overflow-y: auto;
}

.modern-select-option {
    padding: 10px 15px;
    cursor: pointer;
    transition: background 0.2s ease;
    font-size: 14px;
    color: #333;
}

    .modern-select-option:hover {
        background-color: var(--light-background);
        color: var(--theme-default);
    }

    .modern-select-option.selected {
        background-color: var(--light-background);
        color: var(--theme-default);
        font-weight: 500;
    }

/* Scrollbar styling */
.modern-select-options::-webkit-scrollbar {
    width: 6px;
}

.modern-select-options::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.modern-select-options::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 10px;
}

    .modern-select-options::-webkit-scrollbar-thumb:hover {
        background: #3498db;
    }

/* Placeholder rengi */
.modern-select-container .modern-select-selected:empty::before,
.modern-select-container .modern-select-selected:only-child::before {
    content: attr(data-placeholder);
    color: #aaa;
}

/* Modal içinde çalışacak şekilde z-index ayarı */
.modal .modern-select-dropdown {
    z-index: 1100;
}
