﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace SaysanSalesSystem.Data
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    
    public partial class TrocSalesDBEntities : DbContext
    {
        public TrocSalesDBEntities()
            : base("name=TrocSalesDBEntities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<Clients> Clients { get; set; }
        public virtual DbSet<CompanyTypes> CompanyTypes { get; set; }
        public virtual DbSet<DefinitionBranches> DefinitionBranches { get; set; }
        public virtual DbSet<DefinitionCollections> DefinitionCollections { get; set; }
        public virtual DbSet<DefintionInvoiceTypes> DefintionInvoiceTypes { get; set; }
        public virtual DbSet<InvoiceLine> InvoiceLine { get; set; }
        public virtual DbSet<TransferLogs> TransferLogs { get; set; }
        public virtual DbSet<WarehouseTransferItems> WarehouseTransferItems { get; set; }
        public virtual DbSet<DefinitionCities> DefinitionCities { get; set; }
        public virtual DbSet<CLCARD> CLCARD { get; set; }
        public virtual DbSet<INVOICENO> INVOICENO { get; set; }
        public virtual DbSet<MainGroups> MainGroups { get; set; }
        public virtual DbSet<SalesConsultants> SalesConsultants { get; set; }
        public virtual DbSet<SubGroups> SubGroups { get; set; }
        public virtual DbSet<WareHouse> WareHouse { get; set; }
        public virtual DbSet<Invoice> Invoice { get; set; }
        public virtual DbSet<ProductPhotos> ProductPhotos { get; set; }
        public virtual DbSet<Items> Items { get; set; }
    }
}
