﻿using System;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using SaysanSalesSystem.Data;

namespace SaysanSalesSystem.Controllers
{
    public class UserController : Controller
    {
        private const string USER_COOKIE_NAME = "SaysanUser";
        private const int COOKIE_EXPIRE_DAYS = 365;

        [HttpGet]
        public JsonResult GetCurrentUser()
        {
            try
            {
                var currentUser = GetUserFromCookie();
                return Json(new
                {
                    success = true,
                    hasUser = currentUser != null,
                    user = currentUser != null ? new { id = currentUser.Id, name = currentUser.Name } : null
                }, JsonRequestBehavior.AllowGet);
            }
            catch
            {
                return Json(new { success = false, hasUser = false, message = "Kullanıcı bilgisi alınırken hata oluştu." }, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpGet]
        public JsonResult GetBranches()
        {
            try
            {
                using (var context = new TrocSalesDBEntities())
                {
                    var branches = context.DefinitionBranches.ToList();

                    return Json(new { success = true, data = branches }, JsonRequestBehavior.AllowGet);
                }
            }
            catch(Exception Ec)
            {
                return Json(new { success = false, message = "Şube bilgileri alınırken hata oluştu." }, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public JsonResult SetBranch(int branchId)
        {
            try
            {
                using (var context = new TrocSalesDBEntities())
                {
                    var branch = context.DefinitionBranches.FirstOrDefault(x => x.Id == branchId);
                    if (branch == null)
                        return Json(new { success = false, message = "Geçersiz şube seçimi." });

                    var cookie = new HttpCookie(USER_COOKIE_NAME)
                    {
                        Expires = System.DateTime.Now.AddDays(COOKIE_EXPIRE_DAYS),
                        HttpOnly = false,
                        Secure = Request.IsSecureConnection,
                        Path = "/"
                    };

                    cookie.Values["Id"] = branch.Id.ToString();
                    cookie.Values["Name"] = branch.Name;
                    cookie.Values["No"] = branch.No.ToString();
                    Response.Cookies.Add(cookie);

                    return Json(new { success = true, message = $"{branch.Name} şubesi seçildi." });
                }
            }
            catch
            {
                return Json(new { success = false, message = "Şube seçimi sırasında hata oluştu." });
            }
        }

        [HttpPost]
        public JsonResult ClearUser()
        {
            try
            {
                var cookie = new HttpCookie(USER_COOKIE_NAME)
                {
                    Expires = System.DateTime.Now.AddDays(-1)
                };
                Response.Cookies.Add(cookie);
                return Json(new { success = true });
            }
            catch
            {
                return Json(new { success = false, message = "Kullanıcı silinirken hata oluştu." });
            }
        }

        private DefinitionBranches GetUserFromCookie()
        {
            var cookie = Request.Cookies[USER_COOKIE_NAME];
            if (cookie != null && cookie.Values["Id"] != null && int.TryParse(cookie.Values["Id"], out int branchId))
            {
                using (var context = new TrocSalesDBEntities())
                {
                    return context.DefinitionBranches.FirstOrDefault(x => x.Id == branchId);
                }
            }
            return null;
        }
    }
}
