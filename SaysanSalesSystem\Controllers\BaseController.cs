﻿using System.Linq;
using System.Web.Mvc;
using SaysanSalesSystem.Data;

namespace SaysanSalesSystem.Controllers
{
    public class BaseController : Controller
    {
        private const string USER_COOKIE_NAME = "SaysanUser";
        protected DefinitionBranches CurrentUser { get; private set; }

        protected override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            var cookie = Request.Cookies[USER_COOKIE_NAME];
            if (cookie != null && cookie.Values["Id"] != null && int.TryParse(cookie.Values["Id"], out int id))
            {
                filterContext.Controller.ViewBag.CurrentUserId = id;
                filterContext.Controller.ViewBag.CurrentUserName = cookie.Values["Name"];
                filterContext.Controller.ViewBag.CurrentUserName = cookie.Values["No"];
                filterContext.Controller.ViewBag.HasUser = true;
            }
            else
            {
                filterContext.Controller.ViewBag.HasUser = false;
            }
            base.OnActionExecuting(filterContext);
        }

    }
}