﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="TrocSalesDBModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="Clients">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CustomerCode" Type="nvarchar" MaxLength="4000" />
          <Property Name="CustomerTitle" Type="nvarchar" MaxLength="4000" />
          <Property Name="FirstName" Type="nvarchar" MaxLength="4000" />
          <Property Name="LastName" Type="nvarchar" MaxLength="4000" />
          <Property Name="Adress" Type="nvarchar" MaxLength="4000" />
          <Property Name="City" Type="nvarchar" MaxLength="4000" />
          <Property Name="Country" Type="nvarchar" MaxLength="4000" />
          <Property Name="PostCode" Type="nvarchar" MaxLength="4000" />
          <Property Name="TelNo" Type="nvarchar" MaxLength="4000" />
          <Property Name="FaxNo" Type="nvarchar" MaxLength="4000" />
          <Property Name="TaxOffice" Type="nvarchar" MaxLength="4000" />
          <Property Name="TaxNo" Type="nvarchar" MaxLength="4000" />
          <Property Name="TcNo" Type="nvarchar" MaxLength="4000" />
          <Property Name="Email" Type="nvarchar" MaxLength="4000" />
          <Property Name="Town" Type="nvarchar" MaxLength="4000" />
          <Property Name="District" Type="nvarchar" MaxLength="4000" />
          <Property Name="IsTransfer" Type="int" />
          <Property Name="CompanyTypeId" Type="int" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="CreatedDate" Type="datetime" />
        </EntityType>
        <EntityType Name="CompanyTypes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="100" />
          <Property Name="Type" Type="int" />
        </EntityType>
        <EntityType Name="DefinitionBranches">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="500" />
          <Property Name="No" Type="int" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'TrocSalesDB.dbo.DefinitionCities' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="DefinitionCities">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="Name" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <EntityType Name="DefinitionCollections">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="100" />
          <Property Name="LogicalRef" Type="int" />
        </EntityType>
        <EntityType Name="DefintionInvoiceTypes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="200" />
          <Property Name="LogicalRef" Type="int" />
        </EntityType>
        <EntityType Name="Invoice">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="InvoiceType" Type="int" />
          <Property Name="InvoiceNumber" Type="nvarchar" MaxLength="4000" />
          <Property Name="CustomerId" Type="int" />
          <Property Name="WarehouseNumber" Type="int" />
          <Property Name="PaymentRef" Type="int" />
          <Property Name="BranchNumber" Type="int" />
          <Property Name="Date" Type="datetime" />
          <Property Name="CreatedBy" Type="nvarchar" MaxLength="4000" />
          <Property Name="SalesmanRef" Type="int" />
          <Property Name="PreparedRef" Type="int" />
          <Property Name="DocumentType" Type="smallint" />
          <Property Name="CreateDate" Type="datetime" />
          <Property Name="IsTransfer" Type="int" />
          <Property Name="IsDeleted" Type="bit" />
        </EntityType>
        <EntityType Name="InvoiceLine">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ItemRef" Type="int" />
          <Property Name="InvoiceId" Type="int" />
          <Property Name="Amount" Type="int" />
          <Property Name="IsDeleted" Type="bit" />
        </EntityType>
        <EntityType Name="ProductPhotos">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Type" Type="nvarchar" MaxLength="50" />
          <Property Name="ItemId" Type="int" />
          <Property Name="SubGroupName" Type="nvarchar" MaxLength="1000" />
          <Property Name="ImageName" Type="nvarchar" MaxLength="4000" />
          <Property Name="ImagePath" Type="nvarchar" MaxLength="4000" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="IsDeleted" Type="bit" />
        </EntityType>
        <EntityType Name="TransferLogs">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Type" Type="nvarchar" MaxLength="200" />
          <Property Name="Message" Type="nvarchar" MaxLength="4000" />
          <Property Name="TransferId" Type="int" />
          <Property Name="IsError" Type="bit" />
          <Property Name="CreatedLogicalRef" Type="int" />
          <Property Name="CreatedInfo" Type="nvarchar" MaxLength="4000" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ErrorMessage" Type="nvarchar" MaxLength="4000" />
        </EntityType>
        <EntityType Name="WarehouseTransferItems">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="SourceWarehouseId" Type="int" />
          <Property Name="TargetWarehouseId" Type="int" />
          <Property Name="ItemId" Type="int" />
          <Property Name="Quantity" Type="float" />
          <Property Name="TransferDate" Type="datetime" />
          <Property Name="IsTransfer" Type="bit" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="IsDeleted" Type="bit" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'TrocSalesDB.dbo.CLCARD' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="CLCARD">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CustomerCode" Type="varchar" MaxLength="17" />
          <Property Name="CustomerTitle" Type="varchar" MaxLength="201" />
          <Property Name="FirstName" Type="varchar" MaxLength="51" />
          <Property Name="LastName" Type="varchar" MaxLength="51" />
          <Property Name="Adress" Type="varchar" MaxLength="201" />
          <Property Name="City" Type="varchar" MaxLength="21" />
          <Property Name="Country" Type="varchar" MaxLength="41" />
          <Property Name="PostCode" Type="varchar" MaxLength="11" />
          <Property Name="TelNo" Type="varchar" MaxLength="51" />
          <Property Name="FaxNo" Type="varchar" MaxLength="51" />
          <Property Name="TaxOffice" Type="varchar" MaxLength="31" />
          <Property Name="TaxNo" Type="varchar" MaxLength="16" />
          <Property Name="TcNo" Type="varchar" MaxLength="16" />
          <Property Name="Email" Type="varchar" MaxLength="251" />
          <Property Name="Town" Type="varchar" MaxLength="51" />
          <Property Name="District" Type="varchar" MaxLength="51" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'TrocSalesDB.dbo.INVOICENO' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="INVOICENO">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="FICHENO" Type="varchar" MaxLength="18" />
          <Property Name="ORDERNO" Type="varchar" MaxLength="8" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'TrocSalesDB.dbo.Items' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="Items">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="LogicalRef" Type="int" />
          <Property Name="Active" Type="smallint" />
          <Property Name="CardType" Type="smallint" />
          <Property Name="Code" Type="nvarchar" MaxLength="25" />
          <Property Name="Barcode" Type="varchar" MaxLength="101" />
          <Property Name="Name" Type="nvarchar" MaxLength="51" />
          <Property Name="MainGroup" Type="varchar" MaxLength="11" />
          <Property Name="SubGroup" Type="varchar" MaxLength="11" />
          <Property Name="StgrpCode" Type="nvarchar" MaxLength="25" />
          <Property Name="ProducerCode" Type="nvarchar" MaxLength="101" />
          <Property Name="SpeCode" Type="nvarchar" MaxLength="11" />
          <Property Name="CyphCode" Type="nvarchar" MaxLength="11" />
          <Property Name="Vat" Type="float" />
          <Property Name="ListPrice" Type="float" />
          <Property Name="UnitSetRef" Type="int" />
          <Property Name="ImagePath" Type="nvarchar" MaxLength="4000" />
          <Property Name="ModifiedById" Type="int" />
          <Property Name="ModifiedDate" Type="datetime" />
          <Property Name="CreatedById" Type="int" />
          <Property Name="CreatedDate" Type="datetime" />
          <Property Name="IsDeleted" Type="bit" />
          <Property Name="SpeCodeLogicalRef" Type="int" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'TrocSalesDB.dbo.MainGroups' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="MainGroups">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="MainGroup" Type="varchar" MaxLength="11" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'TrocSalesDB.dbo.SalesConsultants' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="SalesConsultants">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Code" Type="smallint" />
          <Property Name="DEFINITION_" Type="varchar" MaxLength="21" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'TrocSalesDB.dbo.SubGroups' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="SubGroups">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="MainGroup" Type="varchar" MaxLength="11" />
          <Property Name="SubGroup" Type="varchar" MaxLength="11" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'TrocSalesDB.dbo.WareHouse' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="WareHouse">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="FirmaNo" Type="smallint" />
          <Property Name="No" Type="smallint" />
          <Property Name="Name" Type="varchar" MaxLength="51" />
        </EntityType>
        <EntityContainer Name="TrocSalesDBModelStoreContainer">
          <EntitySet Name="Clients" EntityType="Self.Clients" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CompanyTypes" EntityType="Self.CompanyTypes" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DefinitionBranches" EntityType="Self.DefinitionBranches" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DefinitionCollections" EntityType="Self.DefinitionCollections" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DefintionInvoiceTypes" EntityType="Self.DefintionInvoiceTypes" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Invoice" EntityType="Self.Invoice" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="InvoiceLine" EntityType="Self.InvoiceLine" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ProductPhotos" EntityType="Self.ProductPhotos" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TransferLogs" EntityType="Self.TransferLogs" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="WarehouseTransferItems" EntityType="Self.WarehouseTransferItems" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DefinitionCities" EntityType="Self.DefinitionCities" store:Type="Tables" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [DefinitionCities].[Id] AS [Id], 
    [DefinitionCities].[Name] AS [Name]
    FROM [dbo].[DefinitionCities] AS [DefinitionCities]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="CLCARD" EntityType="Self.CLCARD" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [CLCARD].[Id] AS [Id], 
    [CLCARD].[CustomerCode] AS [CustomerCode], 
    [CLCARD].[CustomerTitle] AS [CustomerTitle], 
    [CLCARD].[FirstName] AS [FirstName], 
    [CLCARD].[LastName] AS [LastName], 
    [CLCARD].[Adress] AS [Adress], 
    [CLCARD].[City] AS [City], 
    [CLCARD].[Country] AS [Country], 
    [CLCARD].[PostCode] AS [PostCode], 
    [CLCARD].[TelNo] AS [TelNo], 
    [CLCARD].[FaxNo] AS [FaxNo], 
    [CLCARD].[TaxOffice] AS [TaxOffice], 
    [CLCARD].[TaxNo] AS [TaxNo], 
    [CLCARD].[TcNo] AS [TcNo], 
    [CLCARD].[Email] AS [Email], 
    [CLCARD].[Town] AS [Town], 
    [CLCARD].[District] AS [District]
    FROM [dbo].[CLCARD] AS [CLCARD]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="INVOICENO" EntityType="Self.INVOICENO" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [INVOICENO].[Id] AS [Id], 
    [INVOICENO].[FICHENO] AS [FICHENO], 
    [INVOICENO].[ORDERNO] AS [ORDERNO]
    FROM [dbo].[INVOICENO] AS [INVOICENO]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="Items" EntityType="Self.Items" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [Items].[Id] AS [Id], 
    [Items].[LogicalRef] AS [LogicalRef], 
    [Items].[Active] AS [Active], 
    [Items].[CardType] AS [CardType], 
    [Items].[Code] AS [Code], 
    [Items].[Barcode] AS [Barcode], 
    [Items].[Name] AS [Name], 
    [Items].[MainGroup] AS [MainGroup], 
    [Items].[SubGroup] AS [SubGroup], 
    [Items].[StgrpCode] AS [StgrpCode], 
    [Items].[ProducerCode] AS [ProducerCode], 
    [Items].[SpeCode] AS [SpeCode], 
    [Items].[CyphCode] AS [CyphCode], 
    [Items].[Vat] AS [Vat], 
    [Items].[ListPrice] AS [ListPrice], 
    [Items].[UnitSetRef] AS [UnitSetRef], 
    [Items].[ImagePath] AS [ImagePath], 
    [Items].[ModifiedById] AS [ModifiedById], 
    [Items].[ModifiedDate] AS [ModifiedDate], 
    [Items].[CreatedById] AS [CreatedById], 
    [Items].[CreatedDate] AS [CreatedDate], 
    [Items].[IsDeleted] AS [IsDeleted], 
    [Items].[SpeCodeLogicalRef] AS [SpeCodeLogicalRef]
    FROM [dbo].[Items] AS [Items]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="MainGroups" EntityType="Self.MainGroups" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [MainGroups].[Id] AS [Id], 
    [MainGroups].[MainGroup] AS [MainGroup]
    FROM [dbo].[MainGroups] AS [MainGroups]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="SalesConsultants" EntityType="Self.SalesConsultants" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [SalesConsultants].[Id] AS [Id], 
    [SalesConsultants].[Code] AS [Code], 
    [SalesConsultants].[DEFINITION_] AS [DEFINITION_]
    FROM [dbo].[SalesConsultants] AS [SalesConsultants]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="SubGroups" EntityType="Self.SubGroups" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [SubGroups].[Id] AS [Id], 
    [SubGroups].[MainGroup] AS [MainGroup], 
    [SubGroups].[SubGroup] AS [SubGroup]
    FROM [dbo].[SubGroups] AS [SubGroups]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="WareHouse" EntityType="Self.WareHouse" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [WareHouse].[Id] AS [Id], 
    [WareHouse].[FirmaNo] AS [FirmaNo], 
    [WareHouse].[No] AS [No], 
    [WareHouse].[Name] AS [Name]
    FROM [dbo].[WareHouse] AS [WareHouse]</DefiningQuery>
          </EntitySet>
        </EntityContainer>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="TrocSalesDBModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="TrocSalesDBEntities" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="Clients" EntityType="TrocSalesDBModel.Clients" />
          <EntitySet Name="CompanyTypes" EntityType="TrocSalesDBModel.CompanyTypes" />
          <EntitySet Name="DefinitionBranches" EntityType="TrocSalesDBModel.DefinitionBranches" />
          <EntitySet Name="DefinitionCollections" EntityType="TrocSalesDBModel.DefinitionCollections" />
          <EntitySet Name="DefintionInvoiceTypes" EntityType="TrocSalesDBModel.DefintionInvoiceTypes" />
          <EntitySet Name="InvoiceLine" EntityType="TrocSalesDBModel.InvoiceLine" />
          <EntitySet Name="TransferLogs" EntityType="TrocSalesDBModel.TransferLogs" />
          <EntitySet Name="WarehouseTransferItems" EntityType="TrocSalesDBModel.WarehouseTransferItems" />
          <EntitySet Name="DefinitionCities" EntityType="TrocSalesDBModel.DefinitionCities" />
          <EntitySet Name="CLCARD" EntityType="TrocSalesDBModel.CLCARD" />
          <EntitySet Name="INVOICENO" EntityType="TrocSalesDBModel.INVOICENO" />
          <EntitySet Name="MainGroups" EntityType="TrocSalesDBModel.MainGroups" />
          <EntitySet Name="SubGroups" EntityType="TrocSalesDBModel.SubGroups" />
          <EntitySet Name="WareHouse" EntityType="TrocSalesDBModel.WareHouse" />
          <EntitySet Name="Invoice" EntityType="TrocSalesDBModel.Invoice" />
          <EntitySet Name="ProductPhotos" EntityType="TrocSalesDBModel.ProductPhotos" />
          <EntitySet Name="SalesConsultants" EntityType="TrocSalesDBModel.SalesConsultants" />
        </EntityContainer>
        <EntityType Name="Clients">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CustomerCode" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="CustomerTitle" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="FirstName" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="LastName" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="Adress" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="City" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="Country" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="PostCode" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="TelNo" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="FaxNo" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="TaxOffice" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="TaxNo" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="TcNo" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="Email" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="Town" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="District" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="IsTransfer" Type="Int32" />
          <Property Name="CompanyTypeId" Type="Int32" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
        </EntityType>
        <EntityType Name="CompanyTypes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="Type" Type="Int32" />
        </EntityType>
        <EntityType Name="DefinitionBranches">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Name="No" Type="Int32" />
        </EntityType>
        <EntityType Name="DefinitionCollections">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Name="LogicalRef" Type="Int32" />
        </EntityType>
        <EntityType Name="DefintionInvoiceTypes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="LogicalRef" Type="Int32" />
        </EntityType>
        <EntityType Name="InvoiceLine">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ItemRef" Type="Int32" />
          <Property Name="InvoiceId" Type="Int32" />
          <Property Name="Amount" Type="Int32" />
          <Property Name="IsDeleted" Type="Boolean" />
        </EntityType>
        <EntityType Name="TransferLogs">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Type" Type="String" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Name="Message" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="TransferId" Type="Int32" />
          <Property Name="IsError" Type="Boolean" />
          <Property Name="CreatedLogicalRef" Type="Int32" />
          <Property Name="CreatedInfo" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ErrorMessage" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="WarehouseTransferItems">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="SourceWarehouseId" Type="Int32" />
          <Property Name="TargetWarehouseId" Type="Int32" />
          <Property Name="ItemId" Type="Int32" />
          <Property Name="Quantity" Type="Double" />
          <Property Name="TransferDate" Type="DateTime" Precision="3" />
          <Property Name="IsTransfer" Type="Boolean" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="IsDeleted" Type="Boolean" />
        </EntityType>
        <EntityType Name="DefinitionCities">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="Name" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CLCARD">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CustomerCode" Type="String" MaxLength="17" FixedLength="false" Unicode="false" />
          <Property Name="CustomerTitle" Type="String" MaxLength="201" FixedLength="false" Unicode="false" />
          <Property Name="FirstName" Type="String" MaxLength="51" FixedLength="false" Unicode="false" />
          <Property Name="LastName" Type="String" MaxLength="51" FixedLength="false" Unicode="false" />
          <Property Name="Adress" Type="String" MaxLength="201" FixedLength="false" Unicode="false" />
          <Property Name="City" Type="String" MaxLength="21" FixedLength="false" Unicode="false" />
          <Property Name="Country" Type="String" MaxLength="41" FixedLength="false" Unicode="false" />
          <Property Name="PostCode" Type="String" MaxLength="11" FixedLength="false" Unicode="false" />
          <Property Name="TelNo" Type="String" MaxLength="51" FixedLength="false" Unicode="false" />
          <Property Name="FaxNo" Type="String" MaxLength="51" FixedLength="false" Unicode="false" />
          <Property Name="TaxOffice" Type="String" MaxLength="31" FixedLength="false" Unicode="false" />
          <Property Name="TaxNo" Type="String" MaxLength="16" FixedLength="false" Unicode="false" />
          <Property Name="TcNo" Type="String" MaxLength="16" FixedLength="false" Unicode="false" />
          <Property Name="Email" Type="String" MaxLength="251" FixedLength="false" Unicode="false" />
          <Property Name="Town" Type="String" MaxLength="51" FixedLength="false" Unicode="false" />
          <Property Name="District" Type="String" MaxLength="51" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="INVOICENO">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" />
          <Property Name="FICHENO" Type="String" MaxLength="18" FixedLength="false" Unicode="false" />
          <Property Name="ORDERNO" Type="String" MaxLength="8" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="Items">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" />
          <Property Name="LogicalRef" Type="Int32" />
          <Property Name="Active" Type="Int16" />
          <Property Name="CardType" Type="Int16" />
          <Property Name="Code" Type="String" MaxLength="25" FixedLength="false" Unicode="true" />
          <Property Name="Barcode" Type="String" MaxLength="101" FixedLength="false" Unicode="false" />
          <Property Name="Name" Type="String" MaxLength="51" FixedLength="false" Unicode="true" />
          <Property Name="MainGroup" Type="String" MaxLength="11" FixedLength="false" Unicode="false" />
          <Property Name="SubGroup" Type="String" MaxLength="11" FixedLength="false" Unicode="false" />
          <Property Name="StgrpCode" Type="String" MaxLength="25" FixedLength="false" Unicode="true" />
          <Property Name="ProducerCode" Type="String" MaxLength="101" FixedLength="false" Unicode="true" />
          <Property Name="SpeCode" Type="String" MaxLength="11" FixedLength="false" Unicode="true" />
          <Property Name="CyphCode" Type="String" MaxLength="11" FixedLength="false" Unicode="true" />
          <Property Name="Vat" Type="Double" />
          <Property Name="ListPrice" Type="Double" />
          <Property Name="UnitSetRef" Type="Int32" />
          <Property Name="ModifiedById" Type="Int32" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="CreatedById" Type="Int32" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="IsDeleted" Type="Boolean" />
          <Property Name="SpeCodeLogicalRef" Type="Int32" />
        </EntityType>
        <EntityType Name="MainGroups">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" />
          <Property Name="MainGroup" Type="String" MaxLength="11" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="SubGroups">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" />
          <Property Name="MainGroup" Type="String" MaxLength="11" FixedLength="false" Unicode="false" />
          <Property Name="SubGroup" Type="String" MaxLength="11" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="WareHouse">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="FirmaNo" Type="Int16" />
          <Property Name="No" Type="Int16" />
          <Property Name="Name" Type="String" MaxLength="51" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="Invoice">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="InvoiceType" Type="Int32" />
          <Property Name="InvoiceNumber" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="CustomerId" Type="Int32" />
          <Property Name="WarehouseNumber" Type="Int32" />
          <Property Name="PaymentRef" Type="Int32" />
          <Property Name="BranchNumber" Type="Int32" />
          <Property Name="Date" Type="DateTime" Precision="3" />
          <Property Name="CreatedBy" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="SalesmanRef" Type="Int32" />
          <Property Name="PreparedRef" Type="Int32" />
          <Property Name="DocumentType" Type="Int16" />
          <Property Name="CreateDate" Type="DateTime" Precision="3" />
          <Property Name="IsTransfer" Type="Int32" />
          <Property Name="IsDeleted" Type="Boolean" />
        </EntityType>
        <EntityType Name="ProductPhotos">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Type" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="ItemId" Type="Int32" />
          <Property Name="SubGroupName" Type="String" MaxLength="1000" FixedLength="false" Unicode="true" />
          <Property Name="ImageName" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="ImagePath" Type="String" MaxLength="4000" FixedLength="false" Unicode="true" />
          <Property Name="CreatedDate" Type="DateTime" Precision="3" />
          <Property Name="ModifiedDate" Type="DateTime" Precision="3" />
          <Property Name="IsDeleted" Type="Boolean" />
        </EntityType>
        <EntityType Name="SalesConsultants">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Code" Type="Int16" />
          <Property Name="DEFINITION_" Type="String" MaxLength="21" FixedLength="false" Unicode="false" />
        </EntityType>
        </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="TrocSalesDBModelStoreContainer" CdmEntityContainer="TrocSalesDBEntities">
          <EntitySetMapping Name="Clients">
            <EntityTypeMapping TypeName="TrocSalesDBModel.Clients">
              <MappingFragment StoreEntitySet="Clients">
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="CompanyTypeId" ColumnName="CompanyTypeId" />
                <ScalarProperty Name="IsTransfer" ColumnName="IsTransfer" />
                <ScalarProperty Name="District" ColumnName="District" />
                <ScalarProperty Name="Town" ColumnName="Town" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="TcNo" ColumnName="TcNo" />
                <ScalarProperty Name="TaxNo" ColumnName="TaxNo" />
                <ScalarProperty Name="TaxOffice" ColumnName="TaxOffice" />
                <ScalarProperty Name="FaxNo" ColumnName="FaxNo" />
                <ScalarProperty Name="TelNo" ColumnName="TelNo" />
                <ScalarProperty Name="PostCode" ColumnName="PostCode" />
                <ScalarProperty Name="Country" ColumnName="Country" />
                <ScalarProperty Name="City" ColumnName="City" />
                <ScalarProperty Name="Adress" ColumnName="Adress" />
                <ScalarProperty Name="LastName" ColumnName="LastName" />
                <ScalarProperty Name="FirstName" ColumnName="FirstName" />
                <ScalarProperty Name="CustomerTitle" ColumnName="CustomerTitle" />
                <ScalarProperty Name="CustomerCode" ColumnName="CustomerCode" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CompanyTypes">
            <EntityTypeMapping TypeName="TrocSalesDBModel.CompanyTypes">
              <MappingFragment StoreEntitySet="CompanyTypes">
                <ScalarProperty Name="Type" ColumnName="Type" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DefinitionBranches">
            <EntityTypeMapping TypeName="TrocSalesDBModel.DefinitionBranches">
              <MappingFragment StoreEntitySet="DefinitionBranches">
                <ScalarProperty Name="No" ColumnName="No" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DefinitionCollections">
            <EntityTypeMapping TypeName="TrocSalesDBModel.DefinitionCollections">
              <MappingFragment StoreEntitySet="DefinitionCollections">
                <ScalarProperty Name="LogicalRef" ColumnName="LogicalRef" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DefintionInvoiceTypes">
            <EntityTypeMapping TypeName="TrocSalesDBModel.DefintionInvoiceTypes">
              <MappingFragment StoreEntitySet="DefintionInvoiceTypes">
                <ScalarProperty Name="LogicalRef" ColumnName="LogicalRef" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="InvoiceLine">
            <EntityTypeMapping TypeName="TrocSalesDBModel.InvoiceLine">
              <MappingFragment StoreEntitySet="InvoiceLine">
                <ScalarProperty Name="IsDeleted" ColumnName="IsDeleted" />
                <ScalarProperty Name="Amount" ColumnName="Amount" />
                <ScalarProperty Name="InvoiceId" ColumnName="InvoiceId" />
                <ScalarProperty Name="ItemRef" ColumnName="ItemRef" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TransferLogs">
            <EntityTypeMapping TypeName="TrocSalesDBModel.TransferLogs">
              <MappingFragment StoreEntitySet="TransferLogs">
                <ScalarProperty Name="ErrorMessage" ColumnName="ErrorMessage" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedInfo" ColumnName="CreatedInfo" />
                <ScalarProperty Name="CreatedLogicalRef" ColumnName="CreatedLogicalRef" />
                <ScalarProperty Name="IsError" ColumnName="IsError" />
                <ScalarProperty Name="TransferId" ColumnName="TransferId" />
                <ScalarProperty Name="Message" ColumnName="Message" />
                <ScalarProperty Name="Type" ColumnName="Type" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WarehouseTransferItems">
            <EntityTypeMapping TypeName="TrocSalesDBModel.WarehouseTransferItems">
              <MappingFragment StoreEntitySet="WarehouseTransferItems">
                <ScalarProperty Name="IsDeleted" ColumnName="IsDeleted" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="IsTransfer" ColumnName="IsTransfer" />
                <ScalarProperty Name="TransferDate" ColumnName="TransferDate" />
                <ScalarProperty Name="Quantity" ColumnName="Quantity" />
                <ScalarProperty Name="ItemId" ColumnName="ItemId" />
                <ScalarProperty Name="TargetWarehouseId" ColumnName="TargetWarehouseId" />
                <ScalarProperty Name="SourceWarehouseId" ColumnName="SourceWarehouseId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DefinitionCities">
            <EntityTypeMapping TypeName="TrocSalesDBModel.DefinitionCities">
              <MappingFragment StoreEntitySet="DefinitionCities">
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CLCARD">
            <EntityTypeMapping TypeName="TrocSalesDBModel.CLCARD">
              <MappingFragment StoreEntitySet="CLCARD">
                <ScalarProperty Name="District" ColumnName="District" />
                <ScalarProperty Name="Town" ColumnName="Town" />
                <ScalarProperty Name="Email" ColumnName="Email" />
                <ScalarProperty Name="TcNo" ColumnName="TcNo" />
                <ScalarProperty Name="TaxNo" ColumnName="TaxNo" />
                <ScalarProperty Name="TaxOffice" ColumnName="TaxOffice" />
                <ScalarProperty Name="FaxNo" ColumnName="FaxNo" />
                <ScalarProperty Name="TelNo" ColumnName="TelNo" />
                <ScalarProperty Name="PostCode" ColumnName="PostCode" />
                <ScalarProperty Name="Country" ColumnName="Country" />
                <ScalarProperty Name="City" ColumnName="City" />
                <ScalarProperty Name="Adress" ColumnName="Adress" />
                <ScalarProperty Name="LastName" ColumnName="LastName" />
                <ScalarProperty Name="FirstName" ColumnName="FirstName" />
                <ScalarProperty Name="CustomerTitle" ColumnName="CustomerTitle" />
                <ScalarProperty Name="CustomerCode" ColumnName="CustomerCode" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="INVOICENO">
            <EntityTypeMapping TypeName="TrocSalesDBModel.INVOICENO">
              <MappingFragment StoreEntitySet="INVOICENO">
                <ScalarProperty Name="ORDERNO" ColumnName="ORDERNO" />
                <ScalarProperty Name="FICHENO" ColumnName="FICHENO" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Items">
            <EntityTypeMapping TypeName="TrocSalesDBModel.Items">
              <MappingFragment StoreEntitySet="Items">
                <ScalarProperty Name="SpeCodeLogicalRef" ColumnName="SpeCodeLogicalRef" />
                <ScalarProperty Name="IsDeleted" ColumnName="IsDeleted" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="CreatedById" ColumnName="CreatedById" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="ModifiedById" ColumnName="ModifiedById" />
                <ScalarProperty Name="UnitSetRef" ColumnName="UnitSetRef" />
                <ScalarProperty Name="ListPrice" ColumnName="ListPrice" />
                <ScalarProperty Name="Vat" ColumnName="Vat" />
                <ScalarProperty Name="CyphCode" ColumnName="CyphCode" />
                <ScalarProperty Name="SpeCode" ColumnName="SpeCode" />
                <ScalarProperty Name="ProducerCode" ColumnName="ProducerCode" />
                <ScalarProperty Name="StgrpCode" ColumnName="StgrpCode" />
                <ScalarProperty Name="SubGroup" ColumnName="SubGroup" />
                <ScalarProperty Name="MainGroup" ColumnName="MainGroup" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Barcode" ColumnName="Barcode" />
                <ScalarProperty Name="Code" ColumnName="Code" />
                <ScalarProperty Name="CardType" ColumnName="CardType" />
                <ScalarProperty Name="Active" ColumnName="Active" />
                <ScalarProperty Name="LogicalRef" ColumnName="LogicalRef" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="MainGroups">
            <EntityTypeMapping TypeName="TrocSalesDBModel.MainGroups">
              <MappingFragment StoreEntitySet="MainGroups">
                <ScalarProperty Name="MainGroup" ColumnName="MainGroup" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="SubGroups">
            <EntityTypeMapping TypeName="TrocSalesDBModel.SubGroups">
              <MappingFragment StoreEntitySet="SubGroups">
                <ScalarProperty Name="SubGroup" ColumnName="SubGroup" />
                <ScalarProperty Name="MainGroup" ColumnName="MainGroup" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WareHouse">
            <EntityTypeMapping TypeName="TrocSalesDBModel.WareHouse">
              <MappingFragment StoreEntitySet="WareHouse">
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="No" ColumnName="No" />
                <ScalarProperty Name="FirmaNo" ColumnName="FirmaNo" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Invoice">
            <EntityTypeMapping TypeName="TrocSalesDBModel.Invoice">
              <MappingFragment StoreEntitySet="Invoice">
                <ScalarProperty Name="IsDeleted" ColumnName="IsDeleted" />
                <ScalarProperty Name="IsTransfer" ColumnName="IsTransfer" />
                <ScalarProperty Name="CreateDate" ColumnName="CreateDate" />
                <ScalarProperty Name="DocumentType" ColumnName="DocumentType" />
                <ScalarProperty Name="PreparedRef" ColumnName="PreparedRef" />
                <ScalarProperty Name="SalesmanRef" ColumnName="SalesmanRef" />
                <ScalarProperty Name="CreatedBy" ColumnName="CreatedBy" />
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="BranchNumber" ColumnName="BranchNumber" />
                <ScalarProperty Name="PaymentRef" ColumnName="PaymentRef" />
                <ScalarProperty Name="WarehouseNumber" ColumnName="WarehouseNumber" />
                <ScalarProperty Name="CustomerId" ColumnName="CustomerId" />
                <ScalarProperty Name="InvoiceNumber" ColumnName="InvoiceNumber" />
                <ScalarProperty Name="InvoiceType" ColumnName="InvoiceType" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProductPhotos">
            <EntityTypeMapping TypeName="TrocSalesDBModel.ProductPhotos">
              <MappingFragment StoreEntitySet="ProductPhotos">
                <ScalarProperty Name="IsDeleted" ColumnName="IsDeleted" />
                <ScalarProperty Name="ModifiedDate" ColumnName="ModifiedDate" />
                <ScalarProperty Name="CreatedDate" ColumnName="CreatedDate" />
                <ScalarProperty Name="ImagePath" ColumnName="ImagePath" />
                <ScalarProperty Name="ImageName" ColumnName="ImageName" />
                <ScalarProperty Name="SubGroupName" ColumnName="SubGroupName" />
                <ScalarProperty Name="ItemId" ColumnName="ItemId" />
                <ScalarProperty Name="Type" ColumnName="Type" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="SalesConsultants">
            <EntityTypeMapping TypeName="TrocSalesDBModel.SalesConsultants">
              <MappingFragment StoreEntitySet="SalesConsultants">
                <ScalarProperty Name="DEFINITION_" ColumnName="DEFINITION_" />
                <ScalarProperty Name="Code" ColumnName="Code" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="false" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>