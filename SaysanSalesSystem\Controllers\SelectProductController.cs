﻿using SaysanSalesSystem.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace SaysanSalesSystem.Controllers
{
    public class SelectProductController : Controller
    {
        TrocSalesDBEntities db = new TrocSalesDBEntities();

        // GET: SelectProduct
        public JsonResult GetMainCategories()
        {
            var raw = db.MainGroups.ToList();
            var list = raw
                .Select(m => new ProductCardModel
                {
                    Type = "main",
                    MainGroup = m.MainGroup,
                    SubGroup = "",
                    Name = m.MainGroup,
                    LogicalRef = m.Id,
                    Price = 0,
                    HasImage = true,
                    ImagePath = Url.Content("~/Assets/images/car.png")
                })
                .ToList();
            return Json(new { success = true, data = list }, JsonRequestBehavior.AllowGet);
        }

        public JsonResult GetSubCategories(string main)
        {
            var raw = db.SubGroups.Where(s => s.MainGroup == main).ToList();
            var list = raw
                .Select(s => new ProductCardModel
                {
                    Type = "sub",
                    MainGroup = s.MainGroup,
                    SubGroup = s.SubGroup,
                    Name = s.SubGroup,
                    LogicalRef = s.Id,
                    Price = 0,
                    HasImage = true,
                    ImagePath = Url.Content("~/Assets/images/car.png")
                })
                .ToList();
            return Json(new { success = true, data = list }, JsonRequestBehavior.AllowGet);
        }

        public JsonResult GetProducts(string main, string sub)
        {
            var raw = db.Items
                .Where(i => i.MainGroup == main && i.SubGroup == sub && i.IsDeleted == false)
                .ToList();

            var list = raw
                .Select(i => {
                    // Ürünün fotoğrafını bul
                    var photo = db.ProductPhotos
                        .Where(p => p.ItemId == i.LogicalRef && p.IsDeleted == false)
                        .FirstOrDefault();

                    return new ProductCardModel
                    {
                        Type = "product",
                        MainGroup = i.MainGroup,
                        SubGroup = i.SubGroup,
                        Name = i.Name,
                        LogicalRef = i.LogicalRef,
                        Price = i.ListPrice,
                        Code = i.Code,
                        Barcode = i.Barcode,
                        HasImage = photo != null,
                        ImagePath = photo?.ImagePath
                    };
                })
                .ToList();
            return Json(new { success = true, data = list }, JsonRequestBehavior.AllowGet);
        }

        public class ProductCardModel
        {
            public string Type { get; set; }
            public string MainGroup { get; set; }
            public string SubGroup { get; set; }
            public string Name { get; set; }
            public int? LogicalRef { get; set; }
            public double? Price { get; set; }
            public string Code { get; set; }
            public string Barcode { get; set; }
            public bool? HasImage { get; set; }
            public string ImagePath { get; set; }
        }
    }
}