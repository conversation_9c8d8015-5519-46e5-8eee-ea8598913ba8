﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Newtonsoft.Json;
using SaysanSalesSystem.Data;
using static SaysanSalesSystem.Models.DataTableModel;

namespace SaysanSalesSystem.Controllers
{
    public class ClientController : Controller
    {
        TrocSalesDBEntities db = new TrocSalesDBEntities();
        // GET: Client
        public ActionResult Index()
        {
            var clients = db.CLCARD.Take(100).ToList();
            ViewBag.Clients = clients;


            var cities = db.DefinitionCities.ToList();
            ViewBag.Cities = cities;

            var types = db.CompanyTypes.ToList();
            ViewBag.Types = types;


            ViewBag.BreadcrumbTitle = "Müşteriler";
            ViewBag.ShowBreadcrumb = true;
            return View();
        }

        public ActionResult Create()
        {
            var types = db.CompanyTypes.ToList();
            ViewBag.Types = types;
            ViewBag.BreadcrumbTitle = "<PERSON>üşter<PERSON> Ekle";
            ViewBag.ShowBreadcrumb = true;
            return PartialView();
        }
        [HttpPost]
        public ActionResult Create(Clients client)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    client.IsTransfer = 0;
                    var type = db.CompanyTypes.FirstOrDefault(x => x.Type == client.CompanyTypeId);
                    client.CompanyTypeId = type.Id;
                    client.CreatedDate = DateTime.Now;
                    db.Clients.Add(client);
                    db.SaveChanges();

                    return Json(new { success = true, message = "Kayıt başarıyla eklendi." });
                }
                catch (Exception ex)
                {
                    return Json(new { success = false, message = "Hata oluştu: " + ex.Message });
                }
            }

            return Json(new { success = false, message = "Geçersiz veri gönderildi." });
        }

        // DataTableRequest class'ınızı güncelleyin
        public class DataTableRequest
        {
            public int draw { get; set; }
            public int start { get; set; }
            public int length { get; set; }
            public Search search { get; set; }
            public List<Order> order { get; set; }
            public List<Column> columns { get; set; }
            public string customSearch { get; set; } // Özel arama için
        }

        // Controller Action
        [HttpPost]
        public ActionResult GetClientsMore(DataTableRequest request)
        {
            try
            {
                // Toplam kayıt sayısı
                var totalRecords = GetTotalClientCount();

                // Arama filtresi
                var searchValue = !string.IsNullOrEmpty(request.customSearch)
                    ? request.customSearch
                    : request.search?.value;

                // Verileri getir
                var clients = GetFilteredClients(
                    searchValue,
                    request.start,
                    request.length,
                    request.order?.FirstOrDefault()
                );

                // Filtrelenmiş kayıt sayısı
                var filteredRecords = string.IsNullOrEmpty(searchValue)
                    ? totalRecords
                    : GetFilteredClientCount(searchValue);

                var response = new
                {
                    draw = request.draw,
                    recordsTotal = totalRecords,
                    recordsFiltered = filteredRecords,
                    data = clients.Select(c => new
                    {
                        customerCode = c.CustomerCode,
                        customerTitle = c.CustomerTitle,
                        adress = c.Adress,
                        city = c.City,
                        telNo = c.TelNo,
                        taxOffice = c.TaxOffice,
                        taxNo = c.TaxNo,
                        tcNo = c.TcNo
                    }).ToList()
                };

                return Json(response);
            }
            catch (Exception ex)
            {
                // Hata durumunda detaylı log
                Console.WriteLine($"GetClientsMore Error: {ex.Message}");

                return Json(new
                {
                    draw = request.draw,
                    recordsTotal = 0,
                    recordsFiltered = 0,
                    data = new List<object>(),
                    error = ex.Message
                });
            }
        }

        // Yardımcı metodlar (örnektir, kendi implementasyonunuza göre düzenleyin)
        private int GetTotalClientCount()
        {
            // Toplam müşteri sayısı
            return db.CLCARD.Count();
        }

        private int GetFilteredClientCount(string searchValue)
        {
            // Filtrelenmiş müşteri sayısı
            return db.CLCARD
                .Where(c => c.CustomerCode.Contains(searchValue) ||
                           c.CustomerTitle.Contains(searchValue) ||
                           c.City.Contains(searchValue) ||
                           c.TelNo.Contains(searchValue))
                .Count();
        }

        private List<CLCARD> GetFilteredClients(string searchValue, int start, int length, Order order)
        {
            var query = db.CLCARD.AsQueryable();

            // Arama filtresi
            if (!string.IsNullOrEmpty(searchValue))
            {
                query = query.Where(c =>
                    c.CustomerCode.Contains(searchValue) ||
                    c.CustomerTitle.Contains(searchValue) ||
                    c.Adress.Contains(searchValue) ||
                    c.City.Contains(searchValue) ||
                    c.TelNo.Contains(searchValue) ||
                    c.TaxOffice.Contains(searchValue) ||
                    c.TaxNo.Contains(searchValue) ||
                    c.TcNo.Contains(searchValue));
            }

            // Sıralama
            if (order != null)
            {
                var columnName = GetColumnName(order.column);
                if (order.dir == "asc")
                {
                    query = ApplyOrderBy(query, columnName, true);
                }
                else
                {
                    query = ApplyOrderBy(query, columnName, false);
                }
            }

            // Sayfalama
            return query.Skip(start).Take(length).ToList();
        }

        private string GetColumnName(int columnIndex)
        {
            var columns = new[] { "CustomerCode", "CustomerTitle", "Adress", "City", "TelNo", "TaxOffice", "TaxNo", "TcNo" };
            return columnIndex < columns.Length ? columns[columnIndex] : "CustomerCode";
        }

        private IQueryable<CLCARD> ApplyOrderBy(IQueryable<CLCARD> query, string columnName, bool ascending)
        {
            switch (columnName)
            {
                case "CustomerCode":
                    return ascending ? query.OrderBy(c => c.CustomerCode) : query.OrderByDescending(c => c.CustomerCode);
                case "CustomerTitle":
                    return ascending ? query.OrderBy(c => c.CustomerTitle) : query.OrderByDescending(c => c.CustomerTitle);
                case "City":
                    return ascending ? query.OrderBy(c => c.City) : query.OrderByDescending(c => c.City);
                // Diğer kolonlar için de aynı şekilde...
                default:
                    return ascending ? query.OrderBy(c => c.CustomerCode) : query.OrderByDescending(c => c.CustomerCode);
            }
        }
    }
}