﻿<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{C0F7CC5D-CB99-4FF5-B559-59E04242168D}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SaysanSalesSystem</RootNamespace>
    <AssemblyName>SaysanSalesSystem</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Web.Razor">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Webpages">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.7\lib\net45\System.Web.Webpages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Webpages.Deployment">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.7\lib\net45\System.Web.Webpages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Webpages.Razor">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.7\lib\net45\System.Web.Webpages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Helpers">
      <HintPath>..\packages\Microsoft.AspNet.Webpages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\numpad.css" />
    <Content Include="Assets\images\car.png" />
    <Content Include="Assets\images\dewalt.png" />
    <Content Include="Content\Logo_Web.png" />
    <Content Include="Content\Scripts\numpad.js" />
    <Content Include="Content\Scripts\product-selector.js" />
    <Content Include="Content\Scripts\user-selector.js" />
    <Content Include="Content\Select2\modern_select.css" />
    <Content Include="Content\Select2\select2_custom.js" />
    <Content Include="Content\Site.css" />
    <Content Include="Data\TrocSalesDB.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>TrocSalesDB.Context.cs</LastGenOutput>
      <DependentUpon>TrocSalesDB.edmx</DependentUpon>
    </Content>
    <Content Include="Data\TrocSalesDB.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>TrocSalesDB.edmx</DependentUpon>
      <LastGenOutput>TrocSalesDB.cs</LastGenOutput>
    </Content>
    <Content Include="Global.asax" />
    <Content Include="Web.config" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="Controllers\BaseController.cs" />
    <Compile Include="Controllers\ClientController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\InvoiceController.cs" />
    <Compile Include="Controllers\SelectProductController.cs" />
    <Compile Include="Controllers\UserController.cs" />
    <Compile Include="Data\CLCARD.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\Clients.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\CompanyTypes.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\DefinitionBranches.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\DefinitionCities.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\DefinitionCollections.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\DefintionInvoiceTypes.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\Invoice.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\InvoiceLine.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\INVOICENO.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\Items.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\MainGroups.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\ProductPhotos.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\SalesConsultants.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\SubGroups.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\TransferLogs.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\TrocSalesDB.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>TrocSalesDB.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\TrocSalesDB.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\TrocSalesDB.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>TrocSalesDB.edmx</DependentUpon>
    </Compile>
    <Compile Include="Controllers\ProductsController.cs" />
    <Compile Include="Controllers\WarehouseController.cs" />
    <Compile Include="Data\WareHouse.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Data\WarehouseTransferItems.cs">
      <DependentUpon>TrocSalesDB.tt</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Models\DashboardCard.cs" />
    <Compile Include="Models\DataTableModel.cs" />
    <Compile Include="Models\SaveInvoiceRequest.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <EntityDeploy Include="Data\TrocSalesDB.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>TrocSalesDB.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <Content Include="Data\TrocSalesDB.edmx.diagram">
      <DependentUpon>TrocSalesDB.edmx</DependentUpon>
    </Content>
    <None Include="packages.config" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\Shared\_ProductSelectionModal.cshtml" />
    <Content Include="Views\Invoice\Index.cshtml" />
    <Content Include="Views\Invoice\SalesOrder.cshtml" />
    <Content Include="Views\Client\Create.cshtml" />
    <Content Include="Views\Client\Index.cshtml" />
    <Content Include="Views\Products\ProductPhotos.cshtml" />
    <Content Include="Views\Warehouse\WarehouseTransfers.cshtml" />
    <Content Include="Views\Shared\Numpad.cshtml" />
    <Content Include="Views\Shared\_UserSelectionModal.cshtml" />
    <Content Include="Views\Products\MainGroupList.cshtml" />
    <Content Include="Views\Products\SubGroupList.cshtml" />
    <Content Include="Views\Products\ItemList.cshtml" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Views\Base\" />
    <Folder Include="Views\SelectProduct\" />
    <Folder Include="Views\User\" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>51333</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:51333/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>Bu proje bu bilgisayarda olmayan NuGet paketlerine başvuru yapıyor. Bunları indirmek için NuGet Paket Geri Yükleme'yi kullanın. Daha fazla bilgi için, bkz. http://go.microsoft.com/fwlink/?LinkID=322105. Eksik dosya: {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>