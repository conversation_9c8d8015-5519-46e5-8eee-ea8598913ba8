<?xml version="1.0" encoding="utf-8"?>

<!-- web.config dönüşümünün kullanımı hakkında daha fazla bilgi için https://go.microsoft.com/fwlink/?LinkId=125889 adresini ziyaret edin. -->

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <!--
    Aşağıdaki örnekte "SetAttributes" dönüştürmesi, 
yalnızca "Eşleştirme" bulucusu 
    "MyDB" değerine sahip bir öznitelik "adı" bulduğunda "ReleaseSQLServer"ın kullanılmasına izin verecek şekilde "connectionString"in değerini değiştirir.
    
    <connectionStrings>
      <add name="MyDB" 
        connectionString="Data Source=ReleaseSQLServer;Initial Catalog=MyReleaseDB;Integrated Security=True" 
        xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    </connectionStrings>
  -->
  <system.web>
    <!--
      
      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ö<PERSON>, "Replace" dönüşümü web.config dosyanızın 
      <customErrors> bölümünün tamamını değiştirir.
      <system.web> düğümü altında yalnızca bir customErrors bölümü olduğundan, 
      "xdt:Locator" özniteliğini kullanmak gerekmediğine dikkat edin.
      
      <customErrors defaultRedirect="GenericError.htm"
        mode="RemoteOnly" xdt:Transform="Replace">
        <error statusCode="500" redirect="InternalError.htm"/>
      </customErrors>
    -->
  </system.web>
</configuration>