﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="EntityFramework" version="6.2.0" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc.tr" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor.tr" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages.tr" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net461" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net461" />
</packages>