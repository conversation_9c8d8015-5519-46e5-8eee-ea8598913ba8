﻿@{
    ViewBag.Title = "Ana Sayfa";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
 
<div class="row g-3">
    <div class="col-xl-4 col-md-4  col-sm-12 card-container">
        <div class="dashboard-card support-card" onclick="location.href='@Url.Action("ProductPhotos", "Products")'" style="cursor: pointer;">
            <div class="card-body">
                <div class="icon-container">
                    <i class="fas fa-image"></i>
                </div>
                <h2 class="card-title">Fotoğraf Ekleme</h2>
                <p class="card-description">Ürün Fotoğrafı Ekle</p>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-4  col-sm-12 card-container">
        <div class="dashboard-card sales-card" onclick="location.href='@Url.Action("SalesOrder", "Invoice")'">
            <div class="card-body">
                <div class="icon-container">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h2 class="card-title">Satış Siparişi Oluştur</h2>
                <p class="card-description">Satış İşlemleri</p>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-4  col-sm-12 card-container">
        <div class="dashboard-card customers-card" onclick="location.href='@Url.Action("InvoicedReceipt", "Invoice")'">
            <div class="card-body">
                <div class="icon-container">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <h2 class="card-title">Fişli Fatura Oluştur</h2>
                <p class="card-description">Fişli Fatura</p>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-4  col-sm-12 card-container">
        <div class="dashboard-card products-card" onclick="location.href='@Url.Action("WholesaleInvoice", "Invoice")'">
            <div class="card-body">
                <div class="icon-container">
                    <i class="fas fa-box"></i>
                </div>
                <h2 class="card-title">Toptan Satış Faturası</h2>
                <p class="card-description">Fatura Yönetimi</p>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-4  col-sm-12 card-container">
        <div class="dashboard-card stock-card" onclick="location.href='@Url.Action("DispatchNote", "Invoice")'">
            <div class="card-body">
                <div class="icon-container">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h2 class="card-title">Satış İrsaliyesi</h2>
                <p class="card-description">Satış İrsaliyesi</p>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-4  col-sm-12 card-container">
        <div class="dashboard-card reports-card" onclick="location.href='@Url.Action("WarehouseTransfers", "Warehouse")'" style="cursor: pointer;">

            <div class="card-body">
                <div class="icon-container">
                    <i class="fas fa-warehouse"></i>
                </div>
                <h2 class="card-title">Ambarlar Arası Geçiş</h2>
                <p class="card-description">Ambar</p>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-4  col-sm-12 card-container">
        <div class="dashboard-card invoices-card" onclick="location.href='@Url.Action("ProductPrice", "Products")'">
            <div class="card-body">
                <div class="icon-container">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <h2 class="card-title">Fiyat Gör</h2>
                <p class="card-description">Ürün Fiyat Görüntüle</p>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-4  col-sm-12 card-container">
        <div class="dashboard-card support-card" onclick="location.href='@Url.Action("Index", "Client")'">
            <div class="card-body">
                <div class="icon-container">
                    <i class="fas fa-user"></i>
                </div>
                <h2 class="card-title">Cariler</h2>
                <p class="card-description">Cari Ekle</p>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-4  col-sm-12 card-container">
        <div class="dashboard-card settings-card">
            <div class="card-body">
                <div class="icon-container">
                    <i class="fas fa-cog"></i>
                </div>
                <h2 class="card-title">Ayarlar</h2>
                <p class="card-description">Sistem Ayarları</p>
            </div>
        </div>
    </div>

</div>

<script>
    function openInvoice() {
    const url = '@Url.Action("dummyattach", "Invoice")';
    window.open(url, '_blank');
  }
</script>