// product-photo-selector.js

window.ProductPhotoSelector = {
    state: null,
    getSelection: function () {
        return this.state ? this.state.selections.sub : null;
    }
};

$(function () {
    window.showProductPhotoModal = function () {
        $('#productPhotoModal').modal('show');
    };

    var state = {
        step: 1,
        selections: { main: null, sub: null },
        data: { main: [], sub: [] }
    };
    // global nesneyle eşitledim
    ProductPhotoSelector.state = state;

    function showLoader() {
        $('#ppm-loading').removeClass('d-none');
    }
    function hideLoader() {
        $('#ppm-loading').addClass('d-none');
    }

    function loadMain() {
        var cont = '#ppm-main-categories';
        $.ajax({
            url: '/SelectProduct/GetMainCategories',
            method: 'GET',
            dataType: 'json',
            beforeSend: function () {
                $(cont).empty();      // önce temizle
                showLoader();          // loader göster
            },
            success: function (resp) {
                if (!resp.success) return;
                state.data.main = resp.data;
                render('main', resp.data);
                updateBreadcrumb();
            },
            error: function (err) {
                console.error('Ana kategoriler yüklenirken hata', err);
            },
            complete: function () {
                hideLoader();          // loader gizle
            }
        });
    }

    function loadSub() {
        var cont = '#ppm-sub-categories';
        $.ajax({
            url: '/SelectProduct/GetSubCategories',
            method: 'GET',
            dataType: 'json',
            data: { main: state.selections.main.MainGroup },
            beforeSend: function () {
                $(cont).empty();
                showLoader();
            },
            success: function (resp) {
                if (!resp.success) return;
                state.data.sub = resp.data;
                render('sub', resp.data);
                updateBreadcrumb();
            },
            error: function (err) {
                console.error('Alt kategoriler yüklenirken hata', err);
            },
            complete: function () {
                hideLoader();
            }
        });
    }



    function render(type, items) {
        var cont = {
            main: '#ppm-main-categories',
            sub: '#ppm-sub-categories'
        }[type];
        $(cont).empty();

        items.forEach(function (i) {
            // Seçilmiş mi?
            var isSelected = false;
            if (type === 'main') {
                isSelected = state.selections.main && state.selections.main.LogicalRef === i.LogicalRef;
            } else if (type === 'sub') {
                isSelected = state.selections.sub && state.selections.sub.LogicalRef === i.LogicalRef;
            }
            var selClass = isSelected ? ' border-primary' : '';

            var idAttr = 'data-id="' + i.LogicalRef + '" data-type="' + type + '"',
                baseCard = '<div class="col"><div class="card ppm-card' + selClass + '" ' + idAttr + '>',
                bodyOpen = '<div class="card-body text-center">',
                bodyClose = '</div></div></div>',
                html;

            if (type === 'main') {
                html = baseCard +
                    bodyOpen +
                    '<h5 class="ppm-main-title">' + i.Name + '</h5>' +
                    bodyClose;
            }
            else if (type === 'sub') {
                html = baseCard +
                    '<img src="' + (i.ImagePath || 'https://via.placeholder.com/150') + '" class="card-img-top">' +
                    bodyOpen +
                    '<h6>' + i.Name + '</h6>' +
                    bodyClose;
            }

            $(cont).append(html);
        });

        attachCardEvents();
    }

    function attachCardEvents() {
        $('.ppm-card').off('click').on('click', function () {
            var type = $(this).data('type');
            var id = $(this).data('id');
            if (type === 'main') {
                state.selections.main = state.data.main.find(function (x) { return x.LogicalRef === id; });
                state.step = 2;
                showStep(2);
                loadSub();
            } else if (type === 'sub') {
                // Alt kategori seçimi - bu kategorideki ürünleri göstermek için
                var subCategory = state.data.sub.find(function (x) { return x.LogicalRef === id; });

                // Önceki seçimi temizle
                $('.ppm-card[data-type="sub"]').removeClass('border-primary');

                // Yeni seçimi işaretle
                $(this).addClass('border-primary');
                state.selections.sub = subCategory;

                updateSummary();
            }
            updateBreadcrumb();
        });
    }

    function showStep(n) {
        $('.ppm-step').addClass('d-none');
        $('#ppm-step-' + n).removeClass('d-none');
    }

    function updateBreadcrumb() {
        var bc = $('#productPhotoModal .ppm-breadcrumb ol.breadcrumb');
        bc.empty();
        var items = [{ text: 'ANAMENÜ', step: 1 }];
        if (state.selections.main) items.push({ text: state.selections.main.Name, step: 2 });
        items.forEach(function (it, idx) {
            if (idx < items.length - 1) {
                bc.append('<li class="breadcrumb-item"><a href="#" data-step="' + it.step + '">' + it.text + '</a></li>');
            } else {
                bc.append('<li class="breadcrumb-item active" aria-current="page">' + it.text + '</li>');
            }
        });
        bc.find('a[data-step]').off('click').on('click', function (e) {
            e.preventDefault();
            var s = +$(this).data('step');
            state.step = s;
            if (s === 1) {
                state.selections.main = null;
                state.selections.sub = null;
                showStep(1);
                render('main', state.data.main);
            } else if (s === 2) {
                state.selections.sub = null;
                showStep(2);
                render('sub', state.data.sub);
            }
            updateSummary();
            updateBreadcrumb();
        });
    }

    function updateSummary() {
        var hasSelection = state.selections.sub !== null;
        $('#ppm-confirm').text(hasSelection ? 'Seç' : 'Alt Kategori Seçin');
        $('#ppm-confirm').prop('disabled', !hasSelection);
    }

    $('#ppm-reset').on('click', function () {
        state = {
            step: 1,
            selections: { main: null, sub: null },
            data: { main: state.data.main, sub: [] }
        };
        ProductPhotoSelector.state = state;
        showStep(1);
        render('main', state.data.main);
        updateSummary();
        updateBreadcrumb();
    });

    $('#ppm-search').on('input', function () {
        var q = $(this).val().toLocaleLowerCase('tr');
        var list = state.step === 1
            ? state.data.main
            : state.data.sub;

        var filtered = list.filter(function (x) {
            return x.Name.toLocaleLowerCase('tr').includes(q);
        });

        render(
            state.step === 1 ? 'main' : 'sub',
            filtered
        );
    });

    $('#ppm-confirm').on('click', function () {
        if (state.selections.sub) {
            $('#productPhotoModal').modal('hide');
        }
    });

    $('#productPhotoModal')
        .on('show.bs.modal', function () {
            // 1) Tam reset: adımları, seçimleri ve ara verilerini temizle
            state.step = 1;
            state.selections = { main: null, sub: null };
            state.data.sub = [];

            // 2) Arama kutusunu ve summary'i sıfırla
            $('#ppm-search').val('');
            updateSummary();

            // 3) Breadcrumb'ı ve görünümü başa al
            showStep(1);
            updateBreadcrumb();

            // 4) Ana kategorileri yükle (veya önceden yüklüyse sadece render et)
            if (!state.data.main.length) {
                loadMain();
            } else {
                render('main', state.data.main);
            }

            // 5) Arama kutusuna focus
            setTimeout(function () { $('#ppm-search').focus(); }, 300);
        })
        .on('hidden.bs.modal', function () {
            $('#ppm-search').val('');
            updateSummary();
            state.step = 1;
            showStep(1);
            updateBreadcrumb();
        });
});
