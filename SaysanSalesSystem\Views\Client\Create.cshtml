﻿@model dynamic

<style>
        :root {
            --primary-color: #4f46e5;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }


    .modal-header {
        background: var(--psm-primary);
    }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 15px;
        }

        .form-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .form-body {
            padding: 20px;
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding: 15px 0;
            border-bottom: 2px solid #f1f5f9;
        }

        .section-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), #7c3aed);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        .section-icon i {
            color: white;
            font-size: 20px;
        }

        .section-title {
            color: #1e293b;
            font-size: 22px;
            font-weight: 700;
            margin: 0;
        }

        .section-description {
            color: var(--secondary-color);
            font-size: 14px;
            margin: 5px 0 0 0;
        }

        .form-floating {
            position: relative;
        }

        .form-floating > .form-control,
        .form-floating > .form-select {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            padding: 12px 16px;
            transition: all 0.3s ease;
            background-color: #fafafa;
        }

        .form-floating > .form-control:focus,
        .form-floating > .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            background-color: white;
        }

        .form-floating > label {
            color: var(--secondary-color);
            font-weight: 500;
            padding: 12px 16px;
        }

        .required label::after {
            content: ' *';
            color: var(--danger-color);
        }

        .error-message {
            color: var(--danger-color);
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .form-control.is-invalid,
        .form-select.is-invalid {
            border-color: var(--danger-color);
        }

        .form-control.is-invalid ~ .error-message {
            display: block;
        }

        .dynamic-fields {
            transition: all 0.3s ease;
        }

        .form-actions {
            background: #f8fafc;
            padding: 25px 30px;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            border-top: 1px solid #e2e8f0;
        }

        .btn-modern {
            padding: 12px 30px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            border: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            text-decoration: none;
        }

        .btn-secondary-modern {
            background: #64748b;
            color: white;
        }

        .btn-secondary-modern:hover {
            background: #475569;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(100, 116, 139, 0.3);
        }

        .btn-success-modern {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
        }

        .btn-success-modern:hover {
            background: linear-gradient(135deg, #059669, #047857);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
        }

        /* Kompakt düzen için özel spacing */
        .compact-section {
            margin-bottom: 20px;
        }

        .section-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
            margin: 20px 0;
        }

        /* Responsive optimizasyon */
        @@media (max-width: 768px) {
            .form-body {
                padding: 20px;
            }

            .form-actions {
                padding: 20px;
                flex-direction: column;
            }

            .btn-modern {
                width: 100%;
                justify-content: center;
            }
        }
</style>

    <!-- Floating particles -->
    <div class="particles">
        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
        <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
        <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
        <div class="particle" style="left: 40%; animation-delay: 3s;"></div>
        <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
        <div class="particle" style="left: 60%; animation-delay: 5s;"></div>
        <div class="particle" style="left: 70%; animation-delay: 0.5s;"></div>
        <div class="particle" style="left: 80%; animation-delay: 1.5s;"></div>
        <div class="particle" style="left: 90%; animation-delay: 2.5s;"></div>
    </div>

    <!-- Loading overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <div class="main-container">
        <div class="form-card animate__animated animate__fadeInUp">
            <div class="form-body">
                <form id="clientForm" novalidate>
                    <!-- Temel Bilgiler -->
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <h3 class="section-title">Temel Bilgiler</h3>
                            <p class="section-description">Müşterinin temel kimlik bilgileri</p>
                        </div>
                    </div>

                    <div class="row g-3 compact-section">
                       
                        <div class="col-lg-9 col-md-12">
                            <div class="form-floating required">
                                <input type="text" class="form-control" id="CustomerTitle" name="CustomerTitle" placeholder=" " required>
                                <label for="CustomerTitle">Müşteri Ünvanı</label>
                                <div class="error-message">Bu alan zorunludur</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-12">
                            <div class="form-floating required">
                                <select class="form-select" id="CompanyType" name="CompanyTypeId" required>
                                    <option value="">Şirket Türü Seçiniz</option>
                                    <option value="1">Müşteri</option>
                                    <option value="2">Şahıs Şirketi</option>
                                    <option value="3">Grup Şirketi</option>
                                </select>

                                <div class="error-message">Şirket türü seçilmelidir</div>
                            </div>
                        </div>
                    </div>

                    <!-- Vergi/TC Bilgileri (Dinamik) -->
                    <div class="row g-3 compact-section">
                        <div class="col-md-6 dynamic-fields" id="taxFields" style="display:none;">
                            <div class="row g-3">
                                <div class="col-6">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="TaxNo" name="TaxNo" placeholder=" ">
                                        <label for="TaxNo">Vergi No</label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="TaxOffice" name="TaxOffice" placeholder=" ">
                                        <label for="TaxOffice">Vergi Dairesi</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 dynamic-fields" id="tcField" style="display:none;">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="TcNo" name="TcNo" placeholder=" " maxlength="11">
                                <label for="TcNo">TC Kimlik No</label>
                            </div>
                        </div>
                    </div>

                    <div class="section-divider"></div>

                    <!-- Kişi ve İletişim Bilgileri -->
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-address-card"></i>
                        </div>
                        <div>
                            <h3 class="section-title">Kişi ve İletişim Bilgileri</h3>
                            <p class="section-description">İletişim kurulacak kişi ve iletişim detayları</p>
                        </div>
                    </div>

                    <div class="row g-3 compact-section">
                        <div id="firstName" class="col-lg-3 col-md-4">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="FirstName" name="FirstName" placeholder=" ">
                                <label for="FirstName">Ad</label>
                            </div>
                        </div>
                        <div id="lastName" class="col-lg-3 col-md-4">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="LastName" name="LastName" placeholder=" ">
                                <label for="LastName">Soyad</label>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-4">
                            <div class="form-floating">
                                <input type="email" class="form-control" id="Email" name="Email" placeholder=" ">
                                <label for="Email">Email</label>
                                <div class="error-message">Geçerli bir email adresi giriniz</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="form-floating">
                                <input type="tel" class="form-control" id="TelNo" name="TelNo" placeholder=" ">
                                <label for="TelNo">Telefon</label>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3 compact-section">
                        <div class="col-md-3">
                            <div class="form-floating">
                                <input type="tel" class="form-control" id="FaxNo" name="FaxNo" placeholder=" ">
                                <label for="FaxNo">Fax</label>
                            </div>
                        </div>
                    </div>

                    <div class="section-divider"></div>

                    <!-- Adres Bilgileri -->
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div>
                            <h3 class="section-title">Adres Bilgileri</h3>
                            <p class="section-description">Konum ve adres detayları</p>
                        </div>
                    </div>

                    <div class="row g-3 compact-section">
                        <div class="col-lg-3 col-md-4">
                            <div class="form-floating">
                                <input required type="text" class="form-control" id="Country" name="Country" placeholder=" " value="Türkiye">
                                <label for="Country">Ülke</label>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-4">
                            <div class="form-floating">
                                <select required class="form-select" id="City" name="City">
                                    <option value="">Şehir Seçiniz</option>
                                    @foreach (var city in ViewBag.Cities)
                                    {
                                        <option value="@city.Name">@city.Name</option>
                                    }
                                </select>

                            </div>
                        </div>
                        <div class="col-lg-3 col-md-4">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="Town" name="Town" placeholder=" ">
                                <label for="Town">İlçe</label>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="District" name="District" placeholder=" ">
                                <label for="District">Semt</label>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3 compact-section">
                        <div class="col-lg-9 col-md-8">
                            <div class="form-floating">
                                <textarea class="form-control" id="Adress" name="Adress" placeholder=" " style="height: 60px;"></textarea>
                                <label for="Adress">Adres</label>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-4">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="PostCode" name="PostCode" placeholder=" ">
                                <label for="PostCode">Posta Kodu</label>
                            </div>
                        </div>
                    </div>
                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-modern btn-secondary-modern" onclick="clearForm()">
                            <i class="fas fa-times me-2"></i>Temizle
                        </button>
                        <button type="submit" form="clientForm" class="btn btn-modern btn-success-modern">
                            <i class="fas fa-save me-2"></i>Kaydet
                        </button>
                    </div>
                </form>
            </div>

           
        </div>
    </div>
    <script>
        function clearForm() {
            const form = document.getElementById("clientForm");
            if (!form) return;

            // Tüm input, textarea ve select elemanlarını temizle
            form.querySelectorAll("input, textarea, select").forEach(el => {
                if (el.type === "checkbox" || el.type === "radio") {
                    el.checked = false;
                } else {
                    el.value = "";
                }
            });

            // Varsayılan değer gerekiyorsa (örneğin Türkiye)
            document.getElementById("Country").value = "Türkiye";

            // Gerekli alanlara placeholder veya ilk option'u set et
            document.getElementById("CompanyType").selectedIndex = 0;
            document.getElementById("City").selectedIndex = 0;

            // Koşullu alanları gizle
            document.getElementById("taxFields").style.display = "none";
            document.getElementById("tcField").style.display = "none";
        }

        document.addEventListener('DOMContentLoaded', function () {
            const companyTypeSelect = document.getElementById('CompanyType');
            const taxFields = document.getElementById('taxFields');
            const tcField = document.getElementById('tcField');
            const firstName = document.getElementById('firstName');
            const lastName = document.getElementById('lastName');
            const FirstNameInput = document.getElementById('FirstName');
            const LastNameInput = document.getElementById('LastName');
            const tcInput = document.getElementById('TcNo');
            const taxOfficeInput = document.getElementById('TaxOffice');
            const taxNoInput = document.getElementById('TaxNo');

            // Şirket türü değişikliği
            companyTypeSelect.addEventListener('change', function () {
                var val = this.value;

                if (val == 1 || val == 2) {
                    // Bireysel
                    tcField.style.display = '';
                    firstName.style.display = '';
                    lastName.style.display = '';
                    taxFields.style.display = 'none';

                    // Kurumsal alanları temizle
                    taxOfficeInput.value = '';
                    taxNoInput.value = '';

                } else if (val == 3) {
                    // Kurumsal
                    tcField.style.display = 'none';
                    firstName.style.display = 'none';
                    lastName.style.display = 'none';
                    taxFields.style.display = '';

                    // Bireysel alanları temizle
                    tcInput.value = '';
                    FirstNameInput.value = '';
                    LastNameInput.value = '';

                } else {
                    // Hiçbiri seçilmedi
                    tcField.style.display = 'none';
                    lastName.style.display = 'none';
                    firstName.style.display = 'none';
                    taxFields.style.display = 'none';

                    // Tüm alanları temizle
                    tcInput.value = '';
                    FirstNameInput.value = '';
                    LastNameInput.value = '';
                    taxOfficeInput.value = '';
                    taxNoInput.value = '';
                }
            });


            // TC No sadece numara girişi
            tcInput.addEventListener('input', function () {
                this.value = this.value.replace(/[^0-9]/g, '');
                if (this.value.length > 11) {
                    this.value = this.value.slice(0, 11);
                }
            });

            // Telefon formatlaması
            const phoneInputs = document.querySelectorAll('input[type="tel"]');
            phoneInputs.forEach(input => {
                input.addEventListener('input', function () {
                    // Sadece rakam ve + - ( ) boşluk karakterlerine izin ver
                    this.value = this.value.replace(/[^0-9+\-\(\)\s]/g, '');
                });
            });

            // Form gönderimi
            document.getElementById('clientForm').addEventListener('submit', function (e) {
                e.preventDefault();

                // Form validasyonu
                if (!this.checkValidity()) {
                    this.classList.add('was-validated');
                    return;
                }

                // Buton loading durumu
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Kaydediliyor...';
                submitBtn.disabled = true;

                // Form verilerini topla
                const formData = new FormData(this);

                // AJAX ile gönder (kendi controller metodunuzu yazmanız gerekecek)
                fetch('/Client/Create', {
                    method: 'POST',
                    body: formData
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Başarılı
                            toastr.success('Müşteri başarıyla kaydedildi!');
                            clearForm();
                            bootstrap.Modal.getInstance(document.querySelector('#clientModal')).hide();
                            // DataTable'ı yenile (eğer parent window'da varsa)
                            if (typeof refreshTable === 'function') {
                                refreshTable();
                            }
                        } else {
                            toastr.error('Hata: ' + (data.message || 'Kaydetme işlemi başarısız!'));
                        }
                    })
                    .catch(error => {
                        console.error('Hata:', error);
                        toastr.error('Bir hata oluştu!');
                    })
                    .finally(() => {
                        // Buton durumunu eski haline getir
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
            });
        });
    </script>

 