@* _ProductPhotoModal.cshtml *@
<style>
    :root {
        --ppm-primary: #4F46E5;
        --ppm-primary-light: #6366F1;
        --ppm-secondary: #6B7280;
        --ppm-bg: #F8FAFC;
        --ppm-card-bg: #FFFFFF;
        --ppm-text: #111827;
        --ppm-muted: #9CA3AF;
        --ppm-border: #E5E7EB;
        --ppm-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --ppm-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --ppm-success: #059669;
        --ppm-success-light: #10B981;
        --ppm-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .ppm-modal .modal-content {
        height: 100vh;
        border-radius: 0;
        background: var(--ppm-bg);
        color: var(--ppm-text);
        border: none;
        box-shadow: none;
    }

    .ppm-header {
        padding: 1.5rem 2rem;
        background: var(--ppm-gradient);
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    .ppm-header #ppm-search {
        flex: 1;
        max-width: 450px;
        margin: 0 1.5rem;
        border-radius: 16px;
        border: 2px solid rgba(255, 255, 255, 0.2);
        padding: 0.75rem 1.25rem;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        color: #FFFFFF;
        font-size: 0.95rem;
        transition: all 0.3s ease;
    }

    .ppm-header #ppm-search::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .ppm-header #ppm-search:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.25);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    }

    #ppm-reset,
    #ppm-cancel {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        color: #FFFFFF;
        border-radius: 12px;
        padding: 0.6rem 1.2rem;
        font-weight: 500;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    #ppm-reset:hover,
    #ppm-cancel:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-1px);
    }

    #ppm-confirm {
        background: linear-gradient(135deg, var(--ppm-success) 0%, var(--ppm-success-light) 100%);
        border-radius: 12px;
        border: none;
        padding: 0.6rem 1.5rem;
        color: #FFFFFF;
        font-weight: 600;
        box-shadow: var(--ppm-shadow);
        transition: all 0.3s ease;
    }

    #ppm-confirm:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: var(--ppm-shadow-lg);
    }

    #ppm-confirm:disabled {
        background: var(--ppm-muted);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .ppm-body {
        overflow-y: auto;
        max-height: calc(100vh - 100px);
        padding: 2rem;
        position: relative;
        background: var(--ppm-bg);
    }

    .ppm-body .row {
        display: grid;
        grid-template-columns: repeat(auto-fit, 320px);
        justify-content: center;
        gap: 1.5rem;
        margin: 0;
    }

    #ppm-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
    }

    .ppm-card {
        width: 320px;
        background: var(--ppm-card-bg);
        border: 2px solid transparent;
        border-radius: 20px;
        padding: 1.25rem;
        text-align: center;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        box-shadow: var(--ppm-shadow);
    }

    .ppm-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, rgba(79, 70, 229, 0.03) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1;
    }

    .ppm-card:hover::before {
        opacity: 1;
    }

    .ppm-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: var(--ppm-shadow-lg);
        border-color: var(--ppm-primary-light);
    }

    .ppm-card.border-primary {
        border-color: var(--ppm-primary) !important;
        background: linear-gradient(135deg, #EEF2FF 0%, #E0E7FF 100%);
        box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
    }

    .ppm-card img {
        width: 280px;
        height: 280px;
        object-fit: cover;
        display: block;
        margin: 0 auto 1rem auto;
        border-radius: 16px;
        position: relative;
        z-index: 2;
        transition: transform 0.3s ease;
    }

    .ppm-card:hover img {
        transform: scale(1.05);
    }

    .ppm-card h5,
    .ppm-card h6 {
        position: relative;
        z-index: 2;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--ppm-text);
    }

    .ppm-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: rgba(255, 255, 255, 0.7);
        padding: 0 0.75rem;
        font-size: 1.1rem;
    }

    .ppm-breadcrumb .breadcrumb-item a {
        color: #FFFFFF;
        text-decoration: none;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        transition: background-color 0.2s ease;
    }

    .ppm-breadcrumb .breadcrumb-item a:hover {
        background: rgba(255, 255, 255, 0.15);
    }

    .ppm-breadcrumb .breadcrumb-item.active {
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
    }

    .ppm-main-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin: 2rem 0;
        color: var(--ppm-text);
        text-align: center;
    }

    .spinner-border {
        width: 3rem;
        height: 3rem;
        border-width: 0.3rem;
    }

    @@media (max-width: 767.98px) {
        .ppm-header {
            flex-direction: column;
            text-align: center;
            padding: 1rem;
        }

        .ppm-header #ppm-search {
            margin: 1rem 0;
            max-width: 100%;
        }

        .ppm-body {
            padding: 1rem;
        }

        .ppm-body .row {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1rem;
        }

        .ppm-card {
            width: 100%;
            max-width: 320px;
            margin: 0 auto;
        }

        .ppm-card img {
            width: 100%;
            max-width: 280px;
        }
    }

    @@media (max-width: 575.98px) {
        .ppm-header {
            padding: 0.75rem 1rem;
        }

        .ppm-card {
            padding: 1rem;
        }

        .ppm-card img {
            height: 220px;
        }
    }

    .ppm-step {
        animation: fadeIn 0.5s ease-in-out;
    }

    @@keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

<div class="modal fade" id="productPhotoModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content ppm-modal">

            <div class="modal-header ppm-header">
                <div class="container-fluid">
                    <div class="row w-100 gx-2 gy-2">
                        <div class="col-12 col-lg-4 d-flex align-items-center justify-content-start">
                            <nav class="ppm-breadcrumb" aria-label="product-photo-modal-breadcrumb">
                                <ol class="breadcrumb mb-0 bg-transparent p-0"></ol>
                            </nav>
                        </div>
                        <div class="col-12 col-lg-4 d-flex align-items-center justify-content-center">
                            <input type="search" class="form-control" placeholder="Ürün ara..." id="ppm-search">
                        </div>
                        <div class="col-12 col-lg-4 d-flex align-items-center justify-content-end">
                            <button type="button" class="btn btn-outline-secondary me-1" id="ppm-reset">Sıfırla</button>
                            <button type="button" class="btn btn-outline-secondary me-1" id="ppm-cancel" data-bs-dismiss="modal">İptal</button>
                            <button type="button" class="btn btn-primary" id="ppm-confirm">Seç</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-body ppm-body">
                <div id="ppm-loading" class="text-center my-4 d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Yükleniyor...</span>
                    </div>
                </div>

                <div id="ppm-step-1" class="ppm-step">
                    <div class="row" id="ppm-main-categories"></div>
                </div>
                <div id="ppm-step-2" class="ppm-step d-none">
                    <div class="row" id="ppm-sub-categories"></div>
                </div>
            </div>
        </div>
    </div>
</div>
