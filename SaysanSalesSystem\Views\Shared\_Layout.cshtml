﻿<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>@ViewBag.Title - <PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">
    <link href="~/Content/Site.css" rel="stylesheet">
    <link href="~/Content/Select2/modern_select.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet" />
    <link href="~/Content/numpad.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
</head>
<body>

    @{
        var c = Request.Cookies["SaysanUser"];
        string branchId = c?.Values["Id"];
        string branchName = c?.Values["Name"];
        string branchNo = c?.Values["No"];
    }
    @Html.Partial("Numpad")
    @Html.Partial("_UserSelectionModal")

    <div class="content-wrapper">
        <div class="container-fluid">
            @if (ViewBag.ShowBreadcrumb != false)
            {
                <div class="card shadow-sm rounded border-0 mb-4">
                    <div class="card-body position-relative py-4">
                        <div class="position-absolute top-50 start-50 translate-middle">
                            <img src="~/Content/Logo_Web.png" alt="Saysan Logo" class="img-fluid" style="max-height: 60px;">
                        </div>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb px-4 py-2 mb-0 d-inline-flex bg-transparent">
                                <li class="breadcrumb-item">
                                    <a href="/" class="text-decoration-none text-primary fw-semibold">
                                        <i class="fas fa-home me-2"></i> Anasayfa
                                    </a>
                                </li>
                                <li class="breadcrumb-item active fw-semibold text-dark" aria-current="page">
                                    @ViewBag.BreadcrumbTitle
                                </li>
                            </ol>
                        </nav>
                        <div class="position-absolute top-0 end-0 p-3">
                            <div class="d-flex align-items-center gap-3">
                                <span class="user-info-badge">
                                    <i class="fas fa-user me-2"></i>
                                    @branchName
                                </span>
                                <button type="button" class="btn logout-btn" onclick="logoutUser()">
                                    <i class="fas fa-power-off" aria-hidden="true"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="position-relative d-flex justify-content-center align-items-center py-4">
                    <img src="~/Content/Logo_Web.png"
                         alt="Saysan Logo"
                         class="img-fluid"
                         style="max-height: 60px;">
                    <div class="position-absolute end-0 d-flex align-items-center gap-3 ps-3">
                        <span class="user-info-badge">
                            <i class="fas fa-user me-2"></i>@branchName
                        </span>
                        <button type="button" class="btn logout-btn" onclick="logoutUser()">
                            <i class="fas fa-power-off"></i>
                        </button>
                    </div>
                </div>

            }
        </div>
        <div class="container-fluid">
            @RenderBody()
        </div>
    </div>

    @Html.Partial("_ProductSelectionModal")
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script src="~/Content/Select2/select2_custom.js"></script>
    <script src="~/Content/Scripts/product-selector.js"></script>
    <script src="~/Content/Scripts/user-selector.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    @RenderSection("scripts", required: false)
    <script src="~/Content/Scripts/numpad.js"></script>
</body>
    </html>