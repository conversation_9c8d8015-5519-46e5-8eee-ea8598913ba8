@* _ProductSelectionModal.cshtml *@
<style>
    :root {
        --psm-primary: #4F46E5;
        --psm-primary-light: #6366F1;
        --psm-secondary: #6B7280;
        --psm-bg: #F8FAFC;
        --psm-card-bg: #FFFFFF;
        --psm-text: #111827;
        --psm-muted: #9CA3AF;
        --psm-border: #E5E7EB;
        --psm-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --psm-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --psm-success: #059669;
        --psm-success-light: #10B981;
        --psm-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .psm-modal .modal-content {
        height: 100vh;
        border-radius: 0;
        background: var(--psm-bg);
        color: var(--psm-text);
        border: none;
        box-shadow: none;
    }

    .psm-header {
        padding: 1.5rem 2rem;
        background: var(--psm-gradient);
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    .psm-header #psm-search {
        flex: 1;
        max-width: 450px;
        margin: 0 1.5rem;
        border-radius: 16px;
        border: 2px solid rgba(255, 255, 255, 0.2);
        padding: 0.75rem 1.25rem;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        color: #FFFFFF;
        font-size: 0.95rem;
        transition: all 0.3s ease;
    }

    .psm-header #psm-search::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .psm-header #psm-search:focus {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.4);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        outline: none;
    }

    .psm-breadcrumb-btn {
        background: transparent;
        border: none;
        color: #FFFFFF;
        font-weight: 500;
        font-size: 0.9rem;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        transition: background-color 0.2s ease;
    }

    .psm-breadcrumb-btn:hover {
        background: rgba(255, 255, 255, 0.15);
    }

    #psm-reset,
    #psm-cancel {
        border-radius: 12px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        padding: 0.6rem 1.25rem;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        color: #FFFFFF;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-right: 0.5rem;
    }

    #psm-reset:hover,
    #psm-cancel:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-1px);
    }

    #psm-confirm {
        background: linear-gradient(135deg, var(--psm-success) 0%, var(--psm-success-light) 100%);
        border-radius: 12px;
        border: none;
        padding: 0.6rem 1.5rem;
        color: #FFFFFF;
        font-weight: 600;
        box-shadow: var(--psm-shadow);
        transition: all 0.3s ease;
    }

    #psm-confirm:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: var(--psm-shadow-lg);
    }

    #psm-confirm:disabled {
        background: var(--psm-muted);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .psm-body {
        overflow-y: auto;
        max-height: calc(100vh - 100px);
        padding: 2rem;
        position: relative;
        background: var(--psm-bg);
    }

    .psm-body .row {
        display: grid;
        grid-template-columns: repeat(auto-fit, 320px);
        justify-content: center;
        gap: 1.5rem;
        margin: 0;
    }

    #psm-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
    }

    .psm-card {
        width: 320px;
        background: var(--psm-card-bg);
        border: 2px solid transparent;
        border-radius: 20px;
        padding: 1.25rem;
        text-align: center;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        box-shadow: var(--psm-shadow);
    }

    .psm-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, rgba(79, 70, 229, 0.03) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1;
    }

    .psm-card:hover::before {
        opacity: 1;
    }

    .psm-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: var(--psm-shadow-lg);
        border-color: var(--psm-primary-light);
    }

    .psm-card.border-primary {
        border-color: var(--psm-primary) !important;
        background: linear-gradient(135deg, #EEF2FF 0%, #E0E7FF 100%);
        box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
    }

    .psm-card img {
        width: 280px;
        height: 280px;
        object-fit: cover;
        display: block;
        margin: 0 auto 1rem auto;
        border-radius: 16px;
        position: relative;
        z-index: 2;
        transition: transform 0.3s ease;
    }

    .psm-card:hover img {
        transform: scale(1.05);
    }

    .psm-card h5,
    .psm-card h6 {
        position: relative;
        z-index: 2;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--psm-text);
    }

    .psm-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: rgba(255, 255, 255, 0.7);
        padding: 0 0.75rem;
        font-size: 1.1rem;
    }

    .psm-breadcrumb .breadcrumb-item a {
        color: #FFFFFF;
        text-decoration: none;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        transition: background-color 0.2s ease;
    }

    .psm-breadcrumb .breadcrumb-item a:hover {
        background: rgba(255, 255, 255, 0.15);
    }

    .psm-breadcrumb .breadcrumb-item.active {
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
    }

    .psm-main-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin: 2rem 0;
        color: var(--psm-text);
        text-align: center;
    }

    .psm-price {
        margin-top: 0.75rem;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--psm-success);
        position: relative;
        z-index: 2;
    }

    .spinner-border {
        width: 3rem;
        height: 3rem;
        border-width: 0.3rem;
    }

    @@media (max-width: 767.98px) {
        .psm-header {
            flex-direction: column;
            text-align: center;
            padding: 1rem;
        }

        .psm-header #psm-search {
            margin: 1rem 0;
            max-width: 100%;
        }

        .psm-body {
            padding: 1rem;
        }

        .psm-body .row {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1rem;
        }

        .psm-card {
            width: 100%;
            max-width: 320px;
            margin: 0 auto;
        }

        .psm-card img {
            width: 100%;
            max-width: 280px;
        }
    }

    @@media (max-width: 575.98px) {
        .psm-header {
            padding: 0.75rem 1rem;
        }

        .psm-card {
            padding: 1rem;
        }

        .psm-card img {
            height: 220px;
        }
    }

    .psm-step {
        animation: fadeIn 0.5s ease-in-out;
    }

    @@keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

<div class="modal fade" id="productSelectionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content psm-modal">

            <div class="modal-header psm-header">
                <div class="container-fluid">
                    <div class="row w-100 gx-2 gy-2">
                        <div class="col-12 col-lg-4 d-flex align-items-center justify-content-start">
                            <nav class="psm-breadcrumb" aria-label="product-modal-breadcrumb">
                                <ol class="breadcrumb mb-0 bg-transparent p-0"></ol>
                            </nav>
                        </div>
                        <div class="col-12 col-lg-4 d-flex align-items-center justify-content-center">
                            <input type="search" class="form-control" placeholder="Ürün ara..." id="psm-search">
                        </div>
                        <div class="col-12 col-lg-4 d-flex align-items-center justify-content-end">
                            <button type="button" class="btn btn-outline-secondary me-1" id="psm-reset">Sıfırla</button>
                            <button type="button" class="btn btn-outline-secondary me-1" id="psm-cancel" data-bs-dismiss="modal">İptal</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-body psm-body">
                <div id="psm-loading" class="text-center my-4 d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Yükleniyor...</span>
                    </div>
                </div>

                <div id="psm-step-1" class="psm-step">
                    <div class="row" id="psm-main-categories"></div>
                </div>
                <div id="psm-step-2" class="psm-step d-none">
                    <div class="row" id="psm-sub-categories"></div>
                </div>
                <div id="psm-step-3" class="psm-step d-none">
                    <div class="row" id="psm-products"></div>
                </div>
            </div>
        </div>
    </div>
</div>