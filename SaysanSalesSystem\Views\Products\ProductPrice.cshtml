﻿@{
    ViewBag.Title = "Ü<PERSON><PERSON><PERSON> Görüntüleme";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    .price-list-container {
         
        min-height: 100vh;
        padding: 2rem 0;
    }

    .main-card {
        
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .header-section {
       
        border-radius: 20px 20px 0 0;
        padding: 2rem;
        color: white;
    }

    .barcode-section {
         
        backdrop-filter: blur(5px);
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .barcode-input {
        
        border: none;
        border-radius: 12px;
        padding: 1rem 1.5rem;
        font-size: 1.1rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .barcode-input:focus {
        background: white;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    .modern-btn {
        border-radius: 12px;
        padding: 0.8rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .modern-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .btn-primary-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-success-modern {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
    }

    .btn-danger-modern {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
    }

    .table-container {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1.5rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .modern-table {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    }

    .modern-table thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .modern-table thead th {
        border: none;
        padding: 1rem;
        font-weight: 600;
    }

    .modern-table tbody tr {
        transition: all 0.3s ease;
    }

    .modern-table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
        transform: scale(1.01);
    }

    .modern-table tbody td {
        padding: 1rem;
        vertical-align: middle;
        border-color: rgba(0, 0, 0, 0.05);
    }

    .modern-table tfoot {
        background: rgba(102, 126, 234, 0.1);
        font-weight: 600;
    }

    .input-with-numpad input {
        border-radius: 8px;
        border: 1px solid #e1e5e9;
        transition: all 0.3s ease;
    }

    .input-with-numpad input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .numpad-trigger {
        background: #667eea;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 0.5rem;
        transition: all 0.3s ease;
    }

    

    .remove-btn {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        border: none;
        border-radius: 8px;
        color: white;
        padding: 0.5rem;
        transition: all 0.3s ease;
    }

    .remove-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
    }

    .scan-icon {
        font-size: 1.2rem;
        margin-right: 0.5rem;
    }

    @@media (max-width: 768px) {
        .price-list-container {
            padding: 1rem;
        }

        .header-section {
            padding: 1.5rem;
        }

        .barcode-section {
            padding: 1rem;
            margin: 1rem 0;
        }

        .table-container {
            margin: 1rem 0;
            padding: 1rem;
        }
    }

</style>

<div class="price-list-container">
    <div class="container-fluid">
        <div class="main-card">
            <div class="container-fluid header-section mb-4">
                <div class="row align-items-center gy-3">
                    <!-- Barkod Alanı -->
                    <div class="col-12 col-md-4">
                        <label for="Barcode" class="form-label text-white">
                            <i class="fa fa-barcode scan-icon me-1"></i> Barkod
                        </label>
                        <div class="input-group">
                            <input type="text" id="Barcode" name="Barcode" class="form-control barcode-input"
                                   placeholder="Barkod okutun veya manuel girin...">
                            <button type="button" class="btn btn-primary-modern" id="scanBarcodeBtn">
                                <i class="fa fa-barcode"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Başlık -->
                    <div class="col-12 col-md-4 text-center">
                        <h2 class="mb-1">Ürün Fiyat Listesi</h2>
                        <p class="mb-0 opacity-75">Ürünleri seçin ve fiyatlarını görüntüleyin</p>
                    </div>

                    <!-- Butonlar -->
                    <div class="col-12 col-md-4 d-flex justify-content-md-end gap-2 flex-wrap">
                        <button type="button" class="modern-btn btn-primary-modern" onclick="showProductModal()">
                            <i class="fa fa-plus me-2"></i> Ürün Seç
                        </button>
                        <button type="button" class="modern-btn btn-success-modern" onclick="printPriceListInline()">
                            <i class="fa fa-print me-2"></i> Yazdır
                        </button>
                        <button type="button" class="modern-btn btn-danger-modern" onclick="clearProductTable()">
                            <i class="fa fa-trash me-2"></i> Temizle
                        </button>
                    </div>
                </div>
            </div>



            <div class="table-container">
                <div class="d-flex justify-content-between align-items-center mb-3">

                    <span class="badge bg-primary" id="productCount">0 Ürün</span>
                </div>

                <div class="table-responsive">
                    <table class="table modern-table" id="productsTable">
                        <thead>
                            <tr>
                                <th>Ürün Adı</th>
                                <th>Barkod</th>
                                <th>Adet</th>
                                <th>Ürün Kodu</th>
                                <th>Birim Fiyat</th>
                                <th class="text-center">İşlem</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="4" class="text-end"><strong>Toplam Tutar:</strong></td>
                                <td id="totalPrice"><strong>0.00 ₺</strong></td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@section scripts {
    <script>
        $(document).ready(function () {
            updateProductCount();

            $('#psm-confirm').on('click', function () {
                var selections = ProductSelector.getSelections();
                var logicalRefs = selections.map(function (item) {
                    return item.LogicalRef;
                });

                $.ajax({
                    url: '/Invoice/GetSelectedItems',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ logicalRefs: logicalRefs }),
                    success: function (response) {
                        if (response.success) {
                            addMultipleProductsToTable(response.data);
                        } else {
                            alert("Ürünler getirilemedi.");
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('Ürünleri çekerken hata:', error);
                    }
                });
            });
        });

        function updateProductCount() {
            const count = document.querySelectorAll('#productsTable tbody tr').length;
            document.getElementById('productCount').textContent = count + ' Ürün';
        }

        function findProductRow(barcode) {
            const rows = document.querySelectorAll('#productsTable tbody tr');
            for (let row of rows) {
                if (row.getAttribute('data-barcode') === barcode) {
                    return row;
                }
            }
            return null;
        }

        function addProductToTable(product, qty = 1) {
            let row = findProductRow(product.barcode);
            if (row) {
                const qtyInput = row.querySelector(`#qty-${product.id}`);
                qtyInput.value = parseInt(qtyInput.value) + qty;
            } else {
                const tbody = document.querySelector('#productsTable tbody');
                row = document.createElement('tr');
                row.setAttribute('data-barcode', product.barcode);
                row.setAttribute('data-product-id', product.id);
                row.setAttribute('data-product-logRef', product.logicalRef);
                row.innerHTML = `
                        <td>${product.name}</td>
                        <td><span class="badge bg-light text-dark">${product.barcode ?? ''}</span></td>
                         <td>
     <div class="input-with-numpad">
         <input value="${qty}" min="1" type="number" class="form-control numpad-input pe-5" id="qty-${product.id}">
         <button type="button" class="numpad-trigger" data-input-target="qty-${product.id}">
             <i class="fa-solid fa-calculator"></i>
         </button>
     </div>
 </td>
                        <td><span class="badge bg-secondary">${product.code}</span></td>
                        <td><strong>${product.listPrice} ₺</strong></td>
                        <td class="text-center">
                            <button type="button" class="btn remove-btn btn-sm remove-product">
                                <i class="fa fa-trash"></i>
                            </button>
                        </td>
                    `;

                const qtyInput = row.querySelector(`#qty-${product.id}`);
                qtyInput.addEventListener('input', calculateTotalPrice);

                row.querySelector('.remove-product').onclick = function () {
                    row.remove();
                    calculateTotalPrice();
                    updateProductCount();
                };
                tbody.appendChild(row);
            }
            calculateTotalPrice();
            updateProductCount();
        }

        function addMultipleProductsToTable(products) {
            if (!Array.isArray(products)) return;

            products.forEach(product => {
                addProductToTable({
                    id: product.Id,
                    barcode: product.Barcode,
                    name: product.Name,
                    code: product.Code,
                    listPrice: product.ListPrice,
                    logicalRef: product.LogicalRef
                });
            });
        }

        function calculateTotalPrice() {
            let total = 0;
            const rows = document.querySelectorAll('#productsTable tbody tr');

            rows.forEach(row => {
                const qtyInput = row.querySelector('input[type="number"]');
                const priceCell = row.children[4];
                const priceText = priceCell.querySelector('strong').textContent;

                const qty = parseFloat(qtyInput.value) || 0;
                const unitPrice = parseFloat(priceText.replace(' ₺', '')) || 0;

                total += qty * unitPrice;
            });

            document.getElementById('totalPrice').innerHTML = `<strong>${total.toFixed(4)} ₺</strong>`;
        }

        document.getElementById('Barcode').addEventListener('keydown', function (e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const barcode = this.value.trim();
                if (!barcode) return;

                fetch(`/Invoice/FindProductByBarcode?barcode=${encodeURIComponent(barcode)}`)
                    .then(response => response.json())
                    .then(result => {
                        if (result.success && result.data) {
                            const product = {
                                id: result.data.Id,
                                name: result.data.Name,
                                code: result.data.Code,
                                listPrice: result.data.ListPrice,
                                barcode: result.data.Barcode,
                                logicalRef: result.data.LogicalRef
                            };
                            addProductToTable(product);
                            document.getElementById('Barcode').value = '';
                        } else {
                            alert('Barkod ile eşleşen ürün bulunamadı!');
                        }
                    })
                    .catch(() => {
                        alert('Sunucuya bağlanırken hata oluştu!');
                    })
                    .finally(() => {
                        document.getElementById('Barcode').value = '';
                    });
            }
        });

        document.getElementById('scanBarcodeBtn').addEventListener('click', function () {
            document.getElementById('Barcode').focus();
        });
        document.getElementById('productsTable').addEventListener('focusin', function (e) {
            if (e.target.classList.contains('numpad-input')) {
                const inputId = e.target.id;
                const button = document.querySelector(`.numpad-trigger[data-input-target="${inputId}"]`);
                if (button) {
                    button.click(); // NumPad butonunu tetikle
                }
            }
        });


        function clearProductTable() {
            const tbody = document.querySelector('#productsTable tbody');
            tbody.innerHTML = '';
            document.getElementById('totalPrice').innerHTML = '<strong>0.00 ₺</strong>';
            updateProductCount();
        }
 

    function printPriceListInline() {
    const rows = document.querySelectorAll('#productsTable tbody tr');

    let tableContent = '';
    let totalAmount = 0;

    rows.forEach(row => {
        const name = row.cells[0].textContent;
        const barcode = row.cells[1].textContent;
        const qty = row.querySelector('input[type="number"]').value;
        const code = row.cells[3].textContent;
        const price = row.cells[4].textContent.trim();
        const unitPrice = parseFloat(price.replace(' ₺', '').replace(',', '.')) || 0;
        const lineTotal = (parseFloat(qty) * unitPrice).toFixed(2);
        totalAmount += parseFloat(lineTotal);

        tableContent += `
            <tr>
                <td>${name}</td>
                <td>${barcode}</td>
                <td>${qty}</td>
                <td>${code}</td>
                <td>${unitPrice.toFixed(2)} ₺</td>
                <td>${lineTotal} ₺</td>
            </tr>
        `;
    });

    const printArea = document.createElement('div');
    printArea.id = 'printArea';
    printArea.innerHTML = `
        <style>
            @@media print {
                body * {
                    visibility: hidden;
                }
                #printArea, #printArea * {
                    visibility: visible;
                }
                #printArea {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                }
            }
            body {
                font-family: Arial, sans-serif;
                font-size: 14px;
                color: #333;
            }
            h1 {
                text-align: center;
                margin-bottom: 20px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
            }
            th, td {
                border: 1px solid #aaa;
                padding: 8px;
                text-align: left;
            }
            th {
                background-color: #f0f0f0;
            }
            tfoot td {
                font-weight: bold;
                background-color: #f9f9f9;
            }
        </style>
        <h1>Ürün Fiyat Listesi</h1>
        <table>
            <thead>
                <tr>
                    <th>Ürün Adı</th>
                    <th>Barkod</th>
                    <th>Adet</th>
                    <th>Ürün Kodu</th>
                    <th>Birim Fiyat</th>
                    <th>Toplam</th>
                </tr>
            </thead>
            <tbody>
                ${tableContent}
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="5" style="text-align: right;">Genel Toplam:</td>
                    <td>${totalAmount.toFixed(2)} ₺</td>
                </tr>
            </tfoot>
        </table>
    `;

    document.body.appendChild(printArea);
    window.print();
    document.body.removeChild(printArea); // Yazdırma sonrası kaldırılır
}

    </script>
}