﻿class ModernSelect2 {
    constructor(options = {}) {
        // Varsayılan ayarlar
        this.settings = {
            selector: '.modern-select',
            placeholder: 'Seçiniz...',
            searchEnabled: true,
            ...options
        };

        this.init();
        this.setupJQueryExtension();
        this.observeSelectChanges();
    }

    init() {
        // Sayfadaki tüm select elemanlarını bul ve dönüştür
        const selects = document.querySelectorAll(this.settings.selector);

        selects.forEach(select => {
            this.createCustomSelect(select);
        });

        // Modal içindeki selectler için gözlemci oluştur
        this.observeModals();
    }

    createCustomSelect(originalSelect) {
        // Halihazırda dönüştürülmüş mü kontrol et
        if (originalSelect.getAttribute('data-initialized') === 'true') return;

        // Dönüştürüldü olarak işaretle
        originalSelect.setAttribute('data-initialized', 'true');

        // Orjinal select elementinin olay dinleyicileri ve özelliklerini kaydet
        const originalOnClick = originalSelect.getAttribute('onclick');

        // Orjinal select elementini gizle
        originalSelect.style.display = 'none';

        // Yeni custom select container oluştur
        const container = document.createElement('div');
        container.className = 'modern-select-container';
        originalSelect.parentNode.insertBefore(container, originalSelect);
        container.appendChild(originalSelect);

        // Select header oluştur
        const header = document.createElement('div');
        header.className = 'modern-select-header';
        container.appendChild(header);

        // Seçilen değeri göstermek için span
        const selectedText = document.createElement('span');
        selectedText.className = 'modern-select-selected';
        selectedText.textContent = this.settings.placeholder;
        header.appendChild(selectedText);

        // Ok ikonu
        const arrow = document.createElement('div');
        arrow.className = 'modern-select-arrow';
        header.appendChild(arrow);

        // Dropdown içeriği
        const dropdown = document.createElement('div');
        dropdown.className = 'modern-select-dropdown';
        container.appendChild(dropdown);

        // Arama kutusu
        if (this.settings.searchEnabled) {
            const searchContainer = document.createElement('div');
            searchContainer.className = 'modern-select-search';

            const searchInput = document.createElement('input');
            searchInput.type = 'text';
            searchInput.placeholder = 'Ara...';
            searchInput.addEventListener('input', (e) => {
                this.filterOptions(dropdown, e.target.value);
            });

            // Arama kutusu için klavye kontrolü
            searchInput.addEventListener('keydown', (e) => {
                this.handleKeyNavigation(e, container);
            });

            searchContainer.appendChild(searchInput);
            dropdown.appendChild(searchContainer);
        }

        // Options listesi
        const optionsList = document.createElement('ul');
        optionsList.className = 'modern-select-options';
        dropdown.appendChild(optionsList);

        // Seçenekleri oluştur
        Array.from(originalSelect.options).forEach(option => {
            const listItem = document.createElement('li');
            listItem.className = 'modern-select-option';
            listItem.setAttribute('data-value', option.value);
            listItem.textContent = option.textContent;

            if (option.selected) {
                listItem.classList.add('selected');
                selectedText.textContent = option.textContent;
            }

            listItem.addEventListener('click', () => {
                this.selectOption(originalSelect, selectedText, optionsList, listItem, originalOnClick);
            });

            optionsList.appendChild(listItem);
        });

        // Dropdown açma kapama
        header.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleDropdown(container);
        });

        // Dışarı tıklandığında dropdown'ı kapat
        document.addEventListener('click', () => {
            this.closeAllDropdowns();
        });

        // Dropdown içine tıklandığında kapanmasını engelle
        dropdown.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Klavye kontrolü için event listener ekleme
        container.tabIndex = 0; // Klavye olayları için tıklanabilir yap
        container.addEventListener('keydown', (e) => {
            this.handleKeyNavigation(e, container);
        });

        // Change event listener ekle - jQuery val() metodu ile değiştirilince güncelleme için
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                    this.updateVisualSelection(originalSelect);
                }
            });
        });

        observer.observe(originalSelect, { attributes: true });

        // jQuery val() metodunu izlemek için event listener
        originalSelect.addEventListener('change', () => {
            this.updateVisualSelection(originalSelect);
        });
    }

    updateVisualSelection(originalSelect) {
        const container = originalSelect.closest('.modern-select-container');
        if (!container) return;

        const selectedText = container.querySelector('.modern-select-selected');
        const optionsList = container.querySelector('.modern-select-options');
        const options = optionsList.querySelectorAll('.modern-select-option');


        options.forEach(option => option.classList.remove('selected'));


        const selectedValue = originalSelect.value;
        const selectedOption = originalSelect.querySelector(`option[value="${selectedValue}"]`);
        const visualOption = optionsList.querySelector(`.modern-select-option[data-value="${selectedValue}"]`);


        if (visualOption) {
            visualOption.classList.add('selected');
            selectedText.textContent = selectedOption ? selectedOption.textContent : this.settings.placeholder;
        } else {
            selectedText.textContent = this.settings.placeholder;
        }
    }

    toggleDropdown(container) {
        const isOpen = container.classList.contains('open');

        // Tüm açık dropdownları kapat
        this.closeAllDropdowns();

        // Eğer zaten açıksa, kapat ve işlemi sonlandır
        if (isOpen) return;

        // Bu dropdown'ı aç
        container.classList.add('open');

        // Dropdown açıldığında arama kutusunu temizle ve odaklan
        if (this.settings.searchEnabled) {
            setTimeout(() => {
                const searchInput = container.querySelector('.modern-select-search input');
                if (searchInput) {
                    searchInput.value = ''; // Arama kutusunu temizle

                    // Filtrelenmiş seçenekleri sıfırla
                    const options = container.querySelectorAll('.modern-select-option');
                    options.forEach(option => {
                        option.style.display = '';
                    });

                    searchInput.focus();
                }
            }, 100);
        }
    }

    closeAllDropdowns() {
        const openContainers = document.querySelectorAll('.modern-select-container.open');
        openContainers.forEach(container => {
            container.classList.remove('open');
        });
    }

    selectOption(originalSelect, selectedText, optionsList, selectedItem, originalOnClick) {
        // Tüm "selected" sınıflarını kaldır
        const allOptions = optionsList.querySelectorAll('.modern-select-option');
        allOptions.forEach(item => item.classList.remove('selected'));

        // Seçilen öğeye "selected" sınıfı ekle
        selectedItem.classList.add('selected');

        // Seçilen değeri başlığa yaz
        selectedText.textContent = selectedItem.textContent;

        // Orjinal select'in değerini güncelle
        const selectedValue = selectedItem.getAttribute('data-value');
        originalSelect.value = selectedValue;

        // Change event'ini tetikle
        const event = new Event('change', { bubbles: true });
        originalSelect.dispatchEvent(event);

        // Dropdown'ı kapat
        this.closeAllDropdowns();

        // Orjinal onClick fonksiyonunu çalıştır (eğer varsa)
        if (originalOnClick) {
            try {
                // onclick="ProductionGroup(value);" formatında fonksiyonu çağır
                const funcName = originalOnClick.split('(')[0];

                // Global scope'da fonksiyonu bul ve çağır
                if (window[funcName]) {
                    window[funcName](selectedValue);
                } else {
                    // Function adını bul ve eval ile çalıştır (son çare olarak)
                    eval(originalOnClick.replace('value', `'${selectedValue}'`));
                }
            } catch (e) {
                console.error('Error executing original onClick function:', e);
            }
        }
    }

    filterOptions(dropdown, searchText) {
        const options = dropdown.querySelectorAll('.modern-select-option');
        const searchLower = searchText.toLowerCase();

        options.forEach(option => {
            const text = option.textContent.toLowerCase();
            if (text.includes(searchLower)) {
                option.style.display = '';
            } else {
                option.style.display = 'none';
            }
        });
    }

    handleKeyNavigation(e, container) {
        const isOpen = container.classList.contains('open');
        const optionsList = container.querySelector('.modern-select-options');
        const options = Array.from(optionsList.querySelectorAll('.modern-select-option:not([style*="display: none"])'));
        const selectedOption = optionsList.querySelector('.modern-select-option.selected');
        const selectedIndex = options.indexOf(selectedOption);
        const originalSelect = container.querySelector('select');
        const originalOnClick = originalSelect.getAttribute('onclick');

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();

                if (!isOpen) {
                    // Kapalıysa dropdown'ı aç
                    this.toggleDropdown(container);
                } else if (options.length > 0) {
                    // Sonraki seçeneğe git
                    const nextIndex = selectedIndex < 0 ? 0 : (selectedIndex + 1) % options.length;
                    const nextOption = options[nextIndex];
                    const selectedText = container.querySelector('.modern-select-selected');

                    if (nextOption) {
                        this.selectOption(originalSelect, selectedText, optionsList, nextOption, originalOnClick);
                        nextOption.scrollIntoView({ block: 'nearest' });
                    }
                }
                break;

            case 'ArrowUp':
                e.preventDefault();

                if (!isOpen) {
                    // Kapalıysa dropdown'ı aç
                    this.toggleDropdown(container);
                } else if (options.length > 0) {
                    // Önceki seçeneğe git
                    const prevIndex = selectedIndex < 0 ? options.length - 1 : (selectedIndex - 1 + options.length) % options.length;
                    const prevOption = options[prevIndex];
                    const selectedText = container.querySelector('.modern-select-selected');

                    if (prevOption) {
                        this.selectOption(originalSelect, selectedText, optionsList, prevOption, originalOnClick);
                        prevOption.scrollIntoView({ block: 'nearest' });
                    }
                }
                break;

            case 'Enter':
            case 'Escape':
                // Enter veya Escape tuşu - Dropdown aç/kapa
                e.preventDefault();
                if (isOpen) {
                    this.closeAllDropdowns();
                } else {
                    this.toggleDropdown(container);
                }
                break;
        }
    }

    observeModals() {
        // Modal içindeki selectler için MutationObserver kullan
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(node => {
                        // Modal içinde yeni eklenen select elemanlarını kontrol et
                        if (node.nodeType === 1) { // 1 = Element node
                            const newSelects = node.querySelectorAll(this.settings.selector);
                            if (newSelects.length > 0) {
                                newSelects.forEach(select => {
                                    this.createCustomSelect(select);
                                });
                            }

                            // Eğer eklenen node'un kendisi select ise
                            if (node.matches && node.matches(this.settings.selector)) {
                                this.createCustomSelect(node);
                            }
                        }
                    });
                }
            });
        });

        // Tüm dökümanı gözlemle
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }


    setupJQueryExtension() {
        // jQuery mevcut olup olmadığını kontrol et
        if (typeof jQuery !== 'undefined') {
            const self = this;

            // jQuery'nin val metodunu genişlet
            const originalVal = jQuery.fn.val;
            jQuery.fn.val = function (value) {
                if (value === undefined) {
                    // Getter olarak kullanıldığında orijinal metodu çağır
                    return originalVal.call(this);
                } else {
                    // Setter olarak kullanıldığında
                    const result = originalVal.call(this, value);

                    // Modern select kontrolünü ara
                    this.each(function () {
                        if (this.matches && this.matches(self.settings.selector)) {
                            // Custom event tetikle
                            const event = new Event('change', { bubbles: true });
                            this.dispatchEvent(event);

                            // Görsel seçimi güncelle
                            self.updateVisualSelection(this);

                            // Orjinal onClick fonksiyonunu çalıştır (eğer varsa)
                            const originalOnClick = this.getAttribute('onclick');
                            if (originalOnClick) {
                                try {
                                    // onclick="ProductionGroup(value);" formatında fonksiyonu çağır
                                    const funcName = originalOnClick.split('(')[0];

                                    // Global scope'da fonksiyonu bul ve çağır
                                    if (window[funcName]) {
                                        window[funcName](value);
                                    } else {
                                        // Function adını bul ve eval ile çalıştır (son çare olarak)
                                        eval(originalOnClick.replace('value', `'${value}'`));
                                    }
                                } catch (e) {
                                    console.error('Error executing original onClick function:', e);
                                }
                            }
                        }
                    });

                    return result;
                }
            };

            // jQuery'nin html metodunu genişlet
            const originalHtml = jQuery.fn.html;
            jQuery.fn.html = function (content) {
                if (content === undefined) {
                    // Getter olarak kullanıldığında orijinal metodu çağır
                    return originalHtml.call(this);
                } else {
                    // Setter olarak kullanıldığında
                    const result = originalHtml.call(this, content);

                    // Modern select kontrolünü ara
                    this.each(function () {
                        if (this.matches && this.matches(self.settings.selector)) {
                            // Select'in içeriği değiştiği için modern select'i güncelle
                            const container = this.closest('.modern-select-container');
                            if (container) {
                                self.updateSelectOptions(this, container);
                            }
                        }
                    });

                    return result;
                }
            };

            // modernSelectUpdate metodu ekle
            jQuery.fn.modernSelectUpdate = function () {
                return this.each(function () {
                    if (this.matches && this.matches(self.settings.selector)) {
                        const container = this.closest('.modern-select-container');
                        if (container) {
                            self.updateSelectOptions(this, container);
                        } else {
                            self.createCustomSelect(this);
                        }
                    }
                });
            };
        }
    }

    // ModernSelect2 sınıfına yeni metot ekleyin
    observeSelectChanges() {
        // Select elementlerindeki option değişikliklerini izleyen observer
        const selectObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.target.nodeName === 'SELECT') {
                    // Eğer bir select elementine child node eklendiyse (option)
                    const select = mutation.target;

                    // Bu select ModernSelect2 ile initialize edilmiş mi?
                    if (select.matches(this.settings.selector)) {
                        // Modern select container'ı bul
                        const container = select.closest('.modern-select-container');

                        // Container varsa ve select initialize edilmişse güncelle
                        if (container && select.getAttribute('data-initialized') === 'true') {
                            this.updateSelectOptions(select, container);
                        }
                        // Initialize edilmemişse yeni baştan oluştur
                        else if (!select.getAttribute('data-initialized')) {
                            this.createCustomSelect(select);
                        }
                    }
                }
            });
        });

        // Tüm document için observer'ı başlat
        selectObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    updateSelectOptions(select, container) {
        // Eğer dropdown yoksa işlem yapma
        const dropdown = container.querySelector('.modern-select-dropdown');
        const optionsList = container.querySelector('.modern-select-options');
        if (!optionsList) return;

        // Mevcut option listesini temizle
        optionsList.innerHTML = '';

        // Selected text elementini bul
        const selectedText = container.querySelector('.modern-select-selected');

        // Orjinal onClick fonksiyonunu kaydet
        const originalOnClick = select.getAttribute('onclick');

        // Seçili option'ı takip et
        let hasSelectedOption = false;

        // Select içindeki yeni optionları alıp modern-select-option olarak ekle
        Array.from(select.options).forEach(option => {
            const listItem = document.createElement('li');
            listItem.className = 'modern-select-option';
            listItem.setAttribute('data-value', option.value);
            listItem.textContent = option.textContent;

            if (option.selected) {
                listItem.classList.add('selected');
                hasSelectedOption = true;

                if (selectedText) {
                    selectedText.textContent = option.textContent || this.settings.placeholder;
                }
            }

            listItem.addEventListener('click', () => {
                this.selectOption(select, selectedText, optionsList, listItem, originalOnClick);
            });

            optionsList.appendChild(listItem);
        });

        // Eğer hiçbir option seçili değilse placeholder göster
        if (!hasSelectedOption && selectedText) {
            selectedText.textContent = this.settings.placeholder;
        }
    }
}

// Sayfa yüklendiğinde otomatik başlat
document.addEventListener('DOMContentLoaded', () => {
    window.modernSelect = new ModernSelect2();
});