﻿ 
    class NumpadManager {
        constructor() {
        this.numpadInput = document.getElementById("numpadInput");
    this.targetInput = null;
    this.modal = null;
    this.init();
        }

    init() {
        this.modal = new bootstrap.Modal(document.getElementById("numpadModal"));

            // Numpad trigger butonları için event listener
            document.addEventListener('click', (e) => {
                if (e.target.closest('.numpad-trigger')) {
                    const trigger = e.target.closest('.numpad-trigger');
    const targetId = trigger.getAttribute('data-input-target');
    this.targetInput = document.getElementById(targetId);
    this.openNumpad();
                }
            });

            // Sayı butonları
            document.querySelectorAll(".num-btn").forEach(btn => {
        btn.addEventListener("click", () => {
            this.addNumber(btn.textContent);
        });
            });

            // Clear butonu
            document.getElementById("numpadClear").addEventListener("click", () => {
        this.clear();
            });

            // Delete butonu
            document.getElementById("numpadDelete").addEventListener("click", () => {
        this.delete();
            });

            // OK butonu
            document.getElementById("numpadOk").addEventListener("click", () => {
        this.confirm();
            });

            // Modal kapatıldığında temizle
            document.getElementById("numpadModal").addEventListener('hidden.bs.modal', () => {
        this.clear();
            });
        }

    openNumpad() {
            if (this.targetInput) {
        this.numpadInput.value = this.targetInput.value || "";
    this.modal.show();
            }
        }

    addNumber(num) {
            // Nokta kontrolü - sadece bir nokta olabilir
            if (num === '.' && this.numpadInput.value.includes('.')) {
                return;
            }

    this.numpadInput.value += num;
    this.updateDisplay();
        }

    clear() {
        this.numpadInput.value = "1";
    this.updateDisplay();
        }

    delete() {
        this.numpadInput.value = this.numpadInput.value.slice(0, -1);
    this.updateDisplay();
        }

    confirm() {
            if (this.targetInput) {
                const value = this.numpadInput.value || "1";
    this.targetInput.value = value;

    // Input change event'ini tetikle
    this.targetInput.dispatchEvent(new Event('change', {bubbles: true }));
    this.targetInput.dispatchEvent(new Event('input', {bubbles: true }));

    this.modal.hide();
            }
        }

    updateDisplay() {
            // Display'i güncelle ve placeholder'ı yönet
            if (this.numpadInput.value === "") {
        this.numpadInput.placeholder = "0";
            } else {
        this.numpadInput.placeholder = "";
            }
        }
    }

    // Sayfa yüklendiğinde NumpadManager'ı başlat
    document.addEventListener('DOMContentLoaded', () => {
        new NumpadManager();

         
    });

    // Ek özellik: Input'lara odaklanıldığında otomatik numpad açma (isteğe bağlı)
    document.addEventListener('focusin', (e) => {
        if (e.target.matches('.numpad-input') && e.target.getAttribute('data-auto-numpad') === 'true') {
            const trigger = e.target.parentElement.querySelector('.numpad-trigger');
    if (trigger) {
        trigger.click();
            }
        }
    });
 