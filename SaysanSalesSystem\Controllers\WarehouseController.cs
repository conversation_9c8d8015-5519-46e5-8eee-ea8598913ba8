﻿using SaysanSalesSystem.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace SaysanSalesSystem.Controllers
{
    public class WarehouseController : Controller
    {
        TrocSalesDBEntities db = new TrocSalesDBEntities();

        // GET: Warehouse
        public ActionResult Index()
        {
            return View();
        }

        public ActionResult WarehouseTransfers()
        {
            ViewBag.BreadcrumbTitle = "Ambarlar Arası Geçiş";
            ViewBag.ShowBreadcrumb = true;

            var warehouses = db.WareHouse.ToList();

            List<SelectListItem> SourceWarehouse = warehouses
                .Select(k => new SelectListItem
                {
                    Text = k.Name + " ( " + k.No + " ) ",
                    Value = k.Id.ToString(),
                    Selected = (k.Name == "ORGANİZE")
                }).ToList();

            List<SelectListItem> TargetWarehouse = warehouses
                .Select(k => new SelectListItem
                {
                    Text = k.Name + " ( " + k.No + " ) ",
                    Value = k.Id.ToString()
                }).ToList();

            ViewBag.SourceWarehouseId = SourceWarehouse;
            ViewBag.TargetWarehouseId = TargetWarehouse;

            return View();
        }

        public JsonResult SearchProducts(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm) || searchTerm.Length < 2)
                {
                    return Json(new List<object>());
                }

                var products = db.Items
                    .Where(p => p.Name.Contains(searchTerm) ||
                               p.Barcode.Contains(searchTerm))
                    .Select(p => new
                    {
                        Id = p.Id,
                        Barcode = p.Barcode ?? "",
                        Name = p.Name
                    })
                    .Take(10)
                    .ToList();

                return Json(products);
            }
            catch (Exception ex)
            {
                return Json(new List<object>());
            }
        }

        public JsonResult GetByBarcode(string barcode)

        {
            if (string.IsNullOrEmpty(barcode))
            {
                return Json(new { success = false, message = "Barkod Boş Bırakılmaz" }, JsonRequestBehavior.AllowGet);
            }

            var product = db.Items
                .Where(p => p.Barcode == barcode)
                .Select(p => new
                {
                    p.Id,
                    p.Name,
                    p.Barcode
                })
                .FirstOrDefault();

            if (product == null)
            {
                return Json(null, JsonRequestBehavior.AllowGet);
            }

            return Json(product, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public JsonResult TransferItems(List<WarehouseTransferItems> transferItems, int sourceWarehouseId, int targetWarehouseId)
        {
            try
            {
                DateTime now = DateTime.Now;

                // Validate warehouse IDs
                if (sourceWarehouseId <= 0 || targetWarehouseId <= 0)
                {
                    return Json(new { success = false, message = "Çıkış ve giriş ambarları boş geçilemez." }, JsonRequestBehavior.AllowGet);
                }

                if (transferItems == null || !transferItems.Any())
                {
                    return Json(new { success = false, message = "Transfer edilecek listesi boş" }, JsonRequestBehavior.AllowGet);
                }

                foreach (var item in transferItems)
                {
                    var transferRecord = new WarehouseTransferItems
                    {
                        SourceWarehouseId = sourceWarehouseId,
                        TargetWarehouseId = targetWarehouseId,
                        Quantity = item.Quantity,
                        ItemId = item.ItemId,
                        IsTransfer = false,
                        CreatedDate = now,
                        IsDeleted = false
                    };

                    db.WarehouseTransferItems.Add(transferRecord);
                }

                var result = db.SaveChanges();
                if (result >= 1)
                {
                    return Json(new { success = true, message = "İşlem Başarılı" }, JsonRequestBehavior.AllowGet);
                }
                else
                {
                    return Json(new { success = false, message = "İşlem Başarısız" }, JsonRequestBehavior.AllowGet);
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Bir Hata Oluştu" }, JsonRequestBehavior.AllowGet);
            }
        }


    }

}