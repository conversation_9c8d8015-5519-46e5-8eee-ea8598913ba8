// product-photo-manager.js - ProductPhotos sayfası için özel JavaScript

$(function () {
    var currentStep = 1; // 1: <PERSON>, 2: <PERSON>te<PERSON>, 3: <PERSON><PERSON><PERSON><PERSON><PERSON>
    var selectedMain = null;
    var selectedSub = null;

    // Sayfa yüklendiğinde ana kategorileri getir
    loadMainCategories();

    // Arama işlevi
    $('#mainSearch').on('input', function() {
        var searchTerm = $(this).val().toLowerCase();
        filterContent(searchTerm);
    });

    function loadMainCategories() {
        $.ajax({
            url: '/SelectProduct/GetMainCategories',
            method: 'GET',
            dataType: 'json',
            beforeSend: function () {
                $('#contentArea').html('<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Yükleniyor...</span></div></div>');
            },
            success: function (response) {
                if (response.success) {
                    renderMainCategories(response.data);
                    updateBreadcrumb();
                }
            },
            error: function () {
                $('#contentArea').html('<div class="alert alert-danger">Ana kategoriler yüklenirken hata oluştu.</div>');
            }
        });
    }

    function renderMainCategories(categories) {
        var html = '<div class="row">';
        categories.forEach(function(category) {
            html += `
                <div class="col">
                    <div class="card psm-card" onclick="selectMainCategory('${category.MainGroup}', '${category.Name}')">
                        <div class="card-body text-center">
                            <h5 class="psm-main-title">${category.Name}</h5>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        $('#contentArea').html(html);
    }

    window.selectMainCategory = function(mainGroup, mainName) {
        selectedMain = { MainGroup: mainGroup, Name: mainName };
        currentStep = 2;
        loadSubCategories(mainGroup);
    };

    function loadSubCategories(mainGroup) {
        $.ajax({
            url: '/SelectProduct/GetSubCategories',
            method: 'GET',
            dataType: 'json',
            data: { main: mainGroup },
            beforeSend: function () {
                $('#contentArea').html('<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Yükleniyor...</span></div></div>');
            },
            success: function (response) {
                if (response.success) {
                    renderSubCategories(response.data);
                    updateBreadcrumb();
                }
            },
            error: function () {
                $('#contentArea').html('<div class="alert alert-danger">Alt kategoriler yüklenirken hata oluştu.</div>');
            }
        });
    }

    function renderSubCategories(categories) {
        var html = '<div class="row">';
        categories.forEach(function(category) {
            html += `
                <div class="col">
                    <div class="card psm-card" onclick="selectSubCategory('${category.SubGroup}', '${category.Name}')">
                        <img src="${category.ImagePath || 'https://via.placeholder.com/150'}" class="card-img-top">
                        <div class="card-body text-center">
                            <h6>${category.Name}</h6>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        $('#contentArea').html(html);
    }

    window.selectSubCategory = function(subGroup, subName) {
        selectedSub = { SubGroup: subGroup, Name: subName };
        currentStep = 3;
        loadProducts(selectedMain.MainGroup, subGroup);
    };

    function loadProducts(mainGroup, subGroup) {
        $.ajax({
            url: '/SelectProduct/GetProducts',
            method: 'GET',
            dataType: 'json',
            data: { main: mainGroup, sub: subGroup },
            beforeSend: function () {
                $('#contentArea').html('<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Yükleniyor...</span></div></div>');
            },
            success: function (response) {
                if (response.success) {
                    renderProducts(response.data);
                    updateBreadcrumb();
                }
            },
            error: function () {
                $('#contentArea').html('<div class="alert alert-danger">Ürünler yüklenirken hata oluştu.</div>');
            }
        });
    }

    function renderProducts(products) {
        var html = '<div class="row">';
        products.forEach(function(product) {
            var imageSrc = product.ImagePath ? product.ImagePath : 'https://via.placeholder.com/280x280?text=Fotoğraf+Yok';
            html += `
                <div class="col">
                    <div class="card psm-card psm-product-card">
                        <img src="${imageSrc}" class="card-img-top">
                        <div class="card-body text-center">
                            <h6>${product.Name}</h6>
                            <p class="text-muted small">Kod: ${product.Code || 'N/A'}</p>
                            <button class="btn btn-primary btn-sm mt-2" onclick="showPhotoUploadArea(${product.LogicalRef}, '${product.Name.replace(/'/g, "\\'")}', '${product.Code || ''}', '${product.Barcode || ''}')">
                                <i class="fas fa-camera"></i> Fotoğraf Ekle
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        $('#contentArea').html(html);
    }

    function updateBreadcrumb() {
        var bc = $('#mainBreadcrumb');
        bc.empty();

        if (currentStep === 1) {
            bc.append('<li class="breadcrumb-item active">Ana Kategoriler</li>');
        } else if (currentStep === 2) {
            bc.append('<li class="breadcrumb-item"><a href="#" onclick="goToStep(1)">Ana Kategoriler</a></li>');
            bc.append('<li class="breadcrumb-item active">' + selectedMain.Name + '</li>');
        } else if (currentStep === 3) {
            bc.append('<li class="breadcrumb-item"><a href="#" onclick="goToStep(1)">Ana Kategoriler</a></li>');
            bc.append('<li class="breadcrumb-item"><a href="#" onclick="goToStep(2)">' + selectedMain.Name + '</a></li>');
            bc.append('<li class="breadcrumb-item active">' + selectedSub.Name + '</li>');
        }
    }

    window.goToStep = function(step) {
        if (step === 1) {
            currentStep = 1;
            selectedMain = null;
            selectedSub = null;
            loadMainCategories();
        } else if (step === 2 && selectedMain) {
            currentStep = 2;
            selectedSub = null;
            loadSubCategories(selectedMain.MainGroup);
        }
    };

    function filterContent(searchTerm) {
        $('.psm-card').each(function() {
            var cardText = $(this).text().toLowerCase();
            if (cardText.includes(searchTerm)) {
                $(this).closest('.col').show();
            } else {
                $(this).closest('.col').hide();
            }
        });
    }

    // Fotoğraf yükleme fonksiyonları
    window.showPhotoUploadArea = function(itemId, itemName, itemCode, itemBarcode) {
        const area = $("#photoUploadArea");

        area.html(`
            <div class="photo-upload-card active">
                <div class="mb-3">
                    <h4 class="text-primary">
                        <i class="fas fa-camera me-2"></i>
                        ${itemName} - Fotoğraf Yükle
                    </h4>
                    <p class="text-muted mb-0">Ürün Kodu: ${itemCode || 'N/A'}</p>
                    <p class="text-muted">Barkod: ${itemBarcode || 'N/A'}</p>
                </div>

                <div class="mb-3">
                    <input type="file" id="photoFiles" class="form-control" accept="image/*" multiple
                           onchange="previewPhotos()" />
                    <small class="text-muted">Birden fazla fotoğraf seçebilirsiniz</small>
                </div>

                <div id="photoPreviews" class="photo-preview-grid"></div>

                <div class="d-flex justify-content-center gap-2 mt-3">
                    <button class="btn btn-primary" id="savePhoto"
                        onclick="uploadPhotos(${itemId})">
                        <i class="fas fa-upload"></i> Fotoğrafları Kaydet
                    </button>
                    <button class="btn btn-secondary" onclick="clearUploadArea()">
                        <i class="fas fa-times"></i> İptal
                    </button>
                </div>

                <div class="progress upload-progress d-none" id="uploadProgress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 0%"></div>
                </div>
            </div>
        `);

        // Fotoğraf ekleme alanına scroll
        area[0].scrollIntoView({ behavior: 'smooth' });
    };

    window.clearUploadArea = function() {
        $("#photoUploadArea").empty();
    };

    window.previewPhotos = function() {
        const files = $("#photoFiles")[0].files;
        const previewContainer = $("#photoPreviews");
        previewContainer.empty();

        if (files.length === 0) return;

        for (let i = 0; i < files.length; i++) {
            const reader = new FileReader();
            reader.onload = function (e) {
                previewContainer.append(`
                    <div class="photo-preview-item">
                        <img src="${e.target.result}" alt="Preview">
                        <button type="button" class="remove-btn" onclick="removePreview(this)">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `);
            };
            reader.readAsDataURL(files[i]);
        }
    };

    window.removePreview = function(button) {
        $(button).closest('.photo-preview-item').remove();
    };

    window.uploadPhotos = function(itemId) {
        const files = $("#photoFiles")[0].files;
        if (files.length === 0) {
            alert("Lütfen en az bir fotoğraf seçin.");
            return;
        }

        const formData = new FormData();
        for (let i = 0; i < files.length; i++) {
            formData.append("photos", files[i]);
        }
        formData.append("itemId", itemId);

        const progressBar = $("#uploadProgress .progress-bar");
        const progressContainer = $("#uploadProgress");
        progressContainer.removeClass("d-none");

        $.ajax({
            url: '/Products/UploadPhotos',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function () {
                const xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener("progress", function (evt) {
                    if (evt.lengthComputable) {
                        const percentComplete = (evt.loaded / evt.total) * 100;
                        progressBar.css("width", percentComplete + "%");
                    }
                }, false);
                return xhr;
            },
            success: function (result) {
                if (result.success == true) {
                    progressBar.css("width", "100%");
                    setTimeout(() => {
                        alert(result.message + " Fotoğraf başarıyla kaydedildi.");
                        clearUploadArea();
                        // Ürün listesini yenile
                        if (currentStep === 3 && selectedMain && selectedSub) {
                            loadProducts(selectedMain.MainGroup, selectedSub.SubGroup);
                        }
                    }, 500);
                }
                else {
                    progressBar.css("width", "0%");
                    progressContainer.addClass("bg-danger");
                    alert("Fotoğraflar yüklenirken bir hata oluştu: " + result.message);
                }
            },
            error: function (xhr, status, error) {
                console.error(error);
                progressBar.css("width", "0%");
                progressContainer.addClass("bg-danger");
                alert("Fotoğraflar yüklenirken bir hata oluştu: " + error);
            }
        });
    };
});
