﻿
.numpad-container {
    max-width: 400px;
    margin: 0 auto;
}

.numpad-btn {
    height: 60px;
    font-size: 1.5rem;
    font-weight: bold;
    border-radius: 12px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

    .numpad-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .numpad-btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

.num-btn {
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border: 2px solid #dee2e6;
    color: #495057;
}

    .num-btn:hover {
        background: linear-gradient(145deg, #e9ecef, #dee2e6);
        border-color: #adb5bd;
    }

.clear-btn {
    background: linear-gradient(145deg, #dc3545, #c82333);
    border: 2px solid #bd2130;
    color: white;
}

    .clear-btn:hover {
        background: linear-gradient(145deg, #c82333, #bd2130);
        border-color: #a71e2a;
    }

.ok-btn {
    background: linear-gradient(145deg, #28a745, #218838);
    border: 2px solid #1e7e34;
    color: white;
}

    .ok-btn:hover {
        background: linear-gradient(145deg, #218838, #1e7e34);
        border-color: #1c7430;
    }

.delete-btn {
    background: linear-gradient(145deg, #fd7e14, #e55a00);
    border: 2px solid #dc5200;
    color: white;
}

    .delete-btn:hover {
        background: linear-gradient(145deg, #e55a00, #dc5200);
        border-color: #c74c00;
    }

#numpadInput {
    font-size: 1.8rem;
    text-align: center;
    font-weight: bold;
    border: 3px solid #007bff;
    border-radius: 12px;
    background: #f8f9ff;
    height: 60px;
}

.modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 20px 20px 0 0;
    border-bottom: none;
}

.modal-title {
    font-weight: bold;
    font-size: 1.3rem;
}

.btn-close {
    filter: invert(1);
}

.input-with-numpad {
    position: relative;
}

.numpad-trigger {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    border: none;
    background: #007bff;
    color: white;
    border-radius: 8px;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

    .numpad-trigger:hover {
        background: #0056b3;
        transform: translateY(-50%) scale(1.05);
    }

 