﻿@{ ViewBag.Title = "WarehouseTransfers";
    Layout = "~/Views/Shared/_Layout.cshtml"; }

<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: #f8f9fa;
        color: #2c3e50;
        line-height: 1.6;
    }

    .container {
        max-width: 100%;
        padding: 20px;
    }

    .header {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        margin-bottom: 20px;
    }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header p {
            font-size: 1.2rem;
            color: #7f8c8d;
            margin-top: 10px;
        }

    .warehouse-selection, .product-section, .transfer-list, .action-buttons {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        margin-bottom: 20px;
    }

    .warehouse-row {
        display: grid;
        grid-template-columns: 1fr 60px 1fr;
        gap: 20px;
        align-items: center;
    }

    .warehouse-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .warehouse-label, .input-label {
        font-size: 1.2rem;
        font-weight: 500;
        margin-bottom: 8px;
    }

    .warehouse-select {
        padding: 12px 16px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
        background: white;
    }

        .warehouse-select:focus {
            outline: none;
            border-color: #3498db;
        }

    .transfer-arrow {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 1.5rem;
        color: #3498db;
    }

    .section-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .product-input {
        display: grid;
        grid-template-columns: 1fr 1fr auto;
        gap: 15px;
        margin-bottom: 20px;
        align-items: end;
    }

    .input-group {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .input-field {
        padding: 12px 16px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }

        .input-field:focus {
            outline: none;
            border-color: #3498db;
        }

    .barcode-input {
        position: relative;
    }

        .barcode-input i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
        }

        .barcode-input input {
            padding-left: 40px;
        }

    .product-dropdown {
        position: relative;
    }

        .product-dropdown input {
            cursor: pointer;
        }

    .dropdown-list {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 2px solid #e9ecef;
        border-top: none;
        border-radius: 0 0 8px 8px;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }

    .dropdown-item {
        padding: 12px 16px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        border-bottom: 1px solid #f8f9fa;
    }

        .dropdown-item:hover {
            background: #f8f9fa;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

    .add-btn {
        background: #27ae60;
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

        .add-btn:hover {
            background: #219a52;
        }

    .transfer-list {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        overflow: hidden;
    }

    .list-header {
        background: #f8f9fa;
        padding: 20px 25px;
        border-bottom: 1px solid #e9ecef;
    }

    .list-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .list-count {
        background: #3498db;
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .transfer-table {
        width: 100%;
        border-collapse: collapse;
    }

        .transfer-table th, .transfer-table td {
            padding: 20px;
            font-size: 1.2rem;
        }

        .transfer-table th {
            background: #f8f9fa;
            text-align: left;
            font-weight: 600;
            color: #2c3e50;
            border-bottom: 2px solid #e9ecef;
        }

        .transfer-table tr:hover {
            background: #f8f9fa;
        }

    .product-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .product-name {
        font-weight: 500;
        color: #2c3e50;
    }

    .product-code {
        font-size: 0.85rem;
        color: #7f8c8d;
    }

    .quantity-input {
        width: 80px;
        padding: 8px 12px;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        text-align: center;
    }

    .remove-btn {
        background: #e74c3c;
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 6px;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

        .remove-btn:hover {
            background: #c0392b;
        }

    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #7f8c8d;
    }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }

    .action-buttons {
        background: white;
        padding: 20px 25px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        margin-top: 20px;
        display: flex;
        gap: 15px;
        justify-content: flex-end;
    }

    .btn {
        padding: 10px 20px;
        font-size: 1rem;
        border: none;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn-primary {
        background: #3498db;
        color: white;
    }

        .btn-primary:hover {
            background: #2980b9;
        }

    .btn-secondary {
        background: #95a5a6;
        color: white;
    }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

    @@media (max-width: 768px) {
        .warehouse-row {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .transfer-arrow {
            transform: rotate(90deg);
        }

        .product-input {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .transfer-table {
            font-size: 0.9rem;
        }

            .transfer-table th,
            .transfer-table td {
                padding: 10px 12px;
            }

        .action-buttons {
            flex-direction: column;
        }
    }

    .search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-top: none;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }

    .search-result-item {
        padding: 10px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
    }

        .search-result-item:hover {
            background-color: #f8f9fa;
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

    .search-container {
        position: relative;
    }

    .loading {
        padding: 10px;
        text-align: center;
        color: #666;
    }

    .no-results {
        padding: 10px;
        text-align: center;
        color: #999;
    }

    .form-control {
        font-size: 1.2rem;
        padding: 15px;
        width: 100%;
    }
</style>

<div class="container">
    <div class="header">
        <h3>
            <i class="fas fa-exchange-alt"></i>
            Ambarlar Arası Geçiş
        </h3>
    </div>

    <div class="warehouse-selection">
        <div class="warehouse-row">
            <div class="warehouse-group">
                <label class="warehouse-label">Çıkış Ambarı</label>
                @Html.DropDownList("SourceWarehouseId", null, "Çıkış Ambarı", new { @Name = "SourceWarehouseId", @id = "SourceWarehouseId", @class = "form-control  select2-single", required = "", @disabled = "disabled" })
            </div>

            <div class="transfer-arrow">
                <i class="fas fa-arrow-right"></i>
            </div>

            <div class="warehouse-group">
                <label class="warehouse-label">Giriş Ambarı</label>
                @Html.DropDownList("TargetWarehouseId", null, "Giriş Ambarı Seçiniz", new { @Name = "TargetWarehouseId", @id = "TargetWarehouseId", @class = "form-control  select2-single", required = "" })

            </div>
        </div>
    </div>

    <div class="product-section">
        <div class="section-title">
            <i class="fas fa-plus-circle"></i>
            Ürün Ekle
        </div>
        <div class="row">
            <div class="col-md-6">
                <label class="input-label">Barkod</label>
                <div class="barcode-input">
                    <i class="fas fa-barcode"></i>
                    <input type="text" class="input-field" id="barcodeInput" placeholder="Barkod">
                </div>
            </div>
            <div class="col-md-6">
                <label class="input-label">Ürün Ara</label>
                <div class="search-container">
                    <input type="text"
                           id="productSearch"
                           class="form-control"
                           placeholder="Ürün adı veya kodu yazın..."
                           autocomplete="off"
                           oninput="searchProduct(this)"
                           onfocus="if(document.getElementById('searchResults').children.length > 0) document.getElementById('searchResults').style.display = 'block'">
                    <div id="searchResults" class="search-results"></div>
                </div>
                <input type="hidden" name="ItemId" id="ItemId" required>
            </div>
        </div>
    </div>

    <div class="transfer-list">
        <div class="list-header">
            <div class="list-title">
                Transfer Listesi
                <span class="list-count" id="itemCount">0 Ürün</span>
            </div>
        </div>
        <div id="transferTableContainer">
            <div class="empty-state">
                <i class="fas fa-clipboard-list"></i>
                <h3>Transfer listesi boş</h3>
                <p>Transfer edilecek ürünleri yukarıdan ekleyebilirsiniz</p>
            </div>
        </div>
    </div>

    <div class="action-buttons">
        <button class="btn btn-secondary">
            <i class="fas fa-times"></i>
            İptal
        </button>
        <button class="btn btn-primary" onclick="TransferItems();">
            <i class="fas fa-check"></i>
            Transferi Onayla
        </button>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.7.1.js"></script>

<script>
    let transferItems = [];

    function searchProduct(input) {
        const term = input.value.trim();
        const resultsContainer = $('#searchResults');
        resultsContainer.empty();

        if (term.length < 2) {
            resultsContainer.hide();
            return;
        }

        resultsContainer.html('<div class="loading">Yükleniyor...</div>').show();

        $.ajax({
            url: '/Warehouse/SearchProducts',
            type: 'POST',
            data: { searchTerm: term },
            dataType: 'json',
            success: function (products) {
                let html = '';

                if (products && products.length > 0) {
                    products.forEach(p => {
                        const productData = JSON.stringify({ id: p.Id, name: p.Name, barcode: p.Barcode });
                        html += `
        <div class="search-result-item"
             data-product='${productData.replace(/'/g, '&#39;')}'
             onclick="selectProductFromElement(this)">
            <strong>${p.Name}</strong><br>
            <small class="text-muted">Barkod: ${p.Barcode}</small>
        </div>`;
                    });
                } else {
                    html = '<div class="no-results">Sonuç bulunamadı</div>';
                }


                resultsContainer.html(html).show();
            },
            error: function () {
                resultsContainer.html('<div class="no-results">Hata oluştu</div>').show();
            }
        });
    }

    function selectProductFromElement(el) {
        const product = JSON.parse(el.getAttribute('data-product'));
        selectProduct(product.id, product.name, product.barcode);
    }


    function selectProduct(id, name, barcode) {
        $('#ItemId').val(id);
        $('#productSearch').val(`${name} (${barcode})`);
        $('#searchResults').hide();

        const product = {
            Id: id,
            Name: name,
            Code: barcode
        };

        addProductToTransfer(product);
    }

    function addProductToTransfer(product) {
        const existing = transferItems.find(x => x.ItemId === product.Id);

        if (existing) {
            existing.Quantity++;
        } else {
            transferItems.push({
                ItemId: product.Id,
                Name: product.Name,
                Code: product.Code,
                Quantity: 1
            });
        }

        renderTransferList();

        $('#productSearch').val('');
        $('#searchResults').html('').hide();
    }

    function renderTransferList() {
        const container = document.getElementById('transferTableContainer');
        container.innerHTML = '';

        if (transferItems.length === 0) {
            container.innerHTML = `
        <div class="empty-state">
            <i class="fas fa-clipboard-list"></i>
            <h3>Transfer listesi boş</h3>
            <p>Transfer edilecek ürünleri yukarıdan ekleyebilirsiniz</p>
        </div>`;
            document.getElementById('itemCount').textContent = '0 Ürün';
            return;
        }

        let html = `
    <table class="transfer-table">
        <thead>
            <tr>
                <th>Ürün</th>
                <th>Miktar</th>
                <th></th>
            </tr>
        </thead>
        <tbody>`;

        transferItems.forEach((item, index) => {
            html += `
        <tr>
            <td>
                <div class="product-info">
                    <div class="product-name">${item.Name}</div>
                    <div class="product-code">${item.Code}</div>
                </div>
            </td>
            <td>
                <input type="number" class="quantity-input" value="${item.Quantity}" min="1"
                    onchange="updateQuantity(${index}, this.value)">
            </td>
            <td>
                <button class="remove-btn" onclick="removeProduct(${index})">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </td>
        </tr>`;
        });

        html += `
        </tbody>
    </table>`;

        container.innerHTML = html;
        document.getElementById('itemCount').textContent = `${transferItems.length} Ürün`;
    }

    function updateQuantity(index, value) {
        const qty = parseInt(value);
        if (isNaN(qty) || qty < 1) {
            alert('Miktar 1 veya daha büyük olmalıdır.');
            renderTransferList();
            return;
        }
        transferItems[index].Quantity = qty;
        renderTransferList();
    }

    function removeProduct(index) {
        transferItems.splice(index, 1);
        renderTransferList();
    }

    $('#barcodeInput').on('keypress', function (e) {
        if (e.key === 'Enter') {
            const barcode = $(this).val().trim();
            if (!barcode) return;

            $.ajax({
                url: '/Warehouse/GetByBarcode',
                method: 'post',
                data: { barcode: barcode },
                success: function (product) {
                    if (product) {
                        selectProduct(product.Id, product.Name, product.Barcode);
                    } else {
                        alert('Ürün bulunamadı.');
                    }
                },
                error: function () {
                    alert('Ürün aranırken hata oluştu.');
                }
            });

            $("#barcodeInput").val("");
        }
    });

    function TransferItems() {

        if (transferItems.length === 0) {
            alert('Transfer edilecek ürün yok.');
            return;
        }
        if (confirm("Transfer İşlemini Onaylıyor Musunuz?")) {
            const sourceWarehouseId = $('#SourceWarehouseId').val();
            const targetWarehouseId = $('#TargetWarehouseId').val();

            if (!sourceWarehouseId || !targetWarehouseId) {
                alert('Lütfen çıkış ve giriş ambarlarını seçin.');
                return;
            }

            $.ajax({
                url: '/Warehouse/TransferItems',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    transferItems: transferItems,
                    sourceWarehouseId: sourceWarehouseId,
                    targetWarehouseId: targetWarehouseId
                }),
                success: function (data) {

                    alert(data.message);


                    transferItems = [];
                    renderTransferList();
                },
                error: function () {
                    alert('Transfer işlemi sırasında hata oluştu.');
                }
            });
        }
    }
</script>