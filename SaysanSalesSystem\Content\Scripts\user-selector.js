﻿var userCheckCompleted = false;

$(document).ready(function () {
    checkUserStatus();
});

function checkUserStatus() {
    $.get('/User/GetCurrentUser')
        .done(function (response) {
            if (response.success) {
                if (!response.hasUser && !userCheckCompleted) {
                    showUserSelection();
                }
                userCheckCompleted = true;
            } else {
                console.error('Kullanıcı kontrol hatası:', response.message);
            }
        })
        .fail(function () {
            console.error('Kullanıcı kontrolü sırasında bağlantı hatası.');
        });
}

function showUserSelection() {
    $('#blurOverlay').fadeIn(300);
    $('#userSelectionModal').fadeIn(300);
    loadBranches();
}

function hideUserSelection() {
    $('#blurOverlay').fadeOut(300);
    $('#userSelectionModal').fadeOut(300);
}

function loadBranches() {
    $('.loading-spinner').show();

    $.get('/User/GetBranches')
        .done(function (response) {
            if (response.success && response.data) {
                updateBranchCards(response.data);
            } else {
                toastr.error(response.message || 'Şube bilgileri yüklenemedi.');
            }
        })
        .fail(function () {
            toastr.error('Şube bilgileri yüklenirken bağlantı hatası oluştu.');
        })
        .always(function () {
            $('.loading-spinner').hide();
        });
}

function updateBranchCards(branches) {
    var $container = $('#branchContainer').empty();

    // No sütununa göre sırala
    branches.sort(function (a, b) {
        return (a.no || 0) - (b.no || 0);
    });

    // Her bir şube için bir kart oluşturup ekle
    branches.forEach(function (b) {
        var $col = $('<div>', { class: 'col-md-6 col-lg-3' });
        var $card = $('<div>', {
            class: 'card branch-card h-100',
            'data-branch-id': b.Id,
            'data-branch-name': b.Name
        });
        var $body = $('<div>', { class: 'card-body text-center p-4' });

        $body.append(
            $('<div>', { class: 'branch-icon mb-3' }).append(
                $('<i>', { class: 'fas fa-building' })
            ),
            $('<h4>', { class: 'card-title mb-2' }).text(b.Name)
        );

        $card.append($body);
        $col.append($card);
        $container.append($col);
    });
}

$(document).on('click', '.branch-card:not(.disabled)', function () {
    var $this = $(this);
    var branchId = $this.data('branch-id');

    if (branchId <= 0) {
        toastr.error('Geçersiz şube seçimi.');
        return;
    }

    $('.branch-card').removeClass('selected');
    $this.addClass('selected');

    setTimeout(function () {
        selectBranch(branchId);
    }, 500);
});

function selectBranch(branchId) {
    $.post('/User/SetBranch', { branchId: branchId })
        .done(function (response) {
            if (response.success) {
                toastr.success(response.message);
                userCheckCompleted = true;
                setTimeout(function () {
                    hideUserSelection();
                    setTimeout(function () {
                        location.reload();
                    }, 300);
                }, 1000);
            } else {
                toastr.error(response.message || 'Şube seçimi başarısız.');
                $('.branch-card').removeClass('selected');
            }
        })
        .fail(function () {
            toastr.error('Şube seçimi sırasında bağlantı hatası oluştu.');
            $('.branch-card').removeClass('selected');
        });
}

function changeUser() {
    $.post('/User/ClearUser')
        .done(function (response) {
            if (response.success) {
                userCheckCompleted = false;
                showUserSelection();
            } else {
                toastr.error(response.message || 'Kullanıcı değiştirilemedi.');
            }
        })
        .fail(function () {
            toastr.error('Bağlantı hatası oluştu.');
        });
}

function logoutUser() {
    if (confirm('Çıkış yapmak istediğinizden emin misiniz?')) {
        $.post('/User/ClearUser')
            .done(function (response) {
                if (response.success) {
                    toastr.success('Başarıyla çıkış yapıldı.');
                    setTimeout(function () {
                        location.reload();
                    }, 1000);
                } else {
                    toastr.error(response.message || 'Çıkış yapılırken hata oluştu.');
                }
            })
            .fail(function () {
                toastr.error('Bağlantı hatası oluştu.');
            });
    }
}
