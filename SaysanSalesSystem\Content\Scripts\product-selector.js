﻿// product-selector.js

window.ProductSelector = {
    state: null,
    getSelections: function () {
        return this.state ? this.state.selections.products : [];
    }
};

$(function () {
    window.showProductModal = function () {
        $('#productSelectionModal').modal('show');
    };

    var state = {
        step: 1,
        selections: { main: null, sub: null, products: [] },
        data: { main: [], sub: [], products: [] }
    };
    // global nesneyle eşitledim
    ProductSelector.state = state;

    function showLoader() {
        $('#psm-loading').removeClass('d-none');
    }
    function hideLoader() {
        $('#psm-loading').addClass('d-none');
    }

    function loadMain() {
        var cont = '#psm-main-categories';
        $.ajax({
            url: '/SelectProduct/GetMainCategories',
            method: 'GET',
            dataType: 'json',
            beforeSend: function () {
                $(cont).empty();      // önce temizle
                showLoader();          // loader göster
            },
            success: function (resp) {
                if (!resp.success) return;
                state.data.main = resp.data;
                render('main', resp.data);
                updateBreadcrumb();
            },
            error: function (err) {
                console.error('Ana kategoriler yüklenirken hata', err);
            },
            complete: function () {
                hideLoader();          // loader gizle
            }
        });
    }

    function loadSub() {
        var cont = '#psm-sub-categories';
        $.ajax({
            url: '/SelectProduct/GetSubCategories',
            method: 'GET',
            dataType: 'json',
            data: { main: state.selections.main.MainGroup },
            beforeSend: function () {
                $(cont).empty();
                showLoader();
            },
            success: function (resp) {
                if (!resp.success) return;
                state.data.sub = resp.data;
                render('sub', resp.data);
                updateBreadcrumb();
            },
            error: function (err) {
                console.error('Alt kategoriler yüklenirken hata', err);
            },
            complete: function () {
                hideLoader();
            }
        });
    }

    function loadProducts() {
        var cont = '#psm-products';
        $.ajax({
            url: '/SelectProduct/GetProducts',
            method: 'GET',
            dataType: 'json',
            data: {
                main: state.selections.main.MainGroup,
                sub: state.selections.sub.SubGroup
            },
            beforeSend: function () {
                $(cont).empty();
                showLoader();
            },
            success: function (resp) {
                if (!resp.success) return;
                state.data.products = resp.data;
                render('product', resp.data);
                updateBreadcrumb();
            },
            error: function (err) {
                console.error('Ürünler yüklenirken hata', err);
            },
            complete: function () {
                hideLoader();
            }
        });
    }


    function render(type, items) {
        var cont = {
            main: '#psm-main-categories',
            sub: '#psm-sub-categories',
            product: '#psm-products'
        }[type];
        $(cont).empty();

        items.forEach(function (i) {
            // Seçilmiş mi?
            var isSelected = false;
            if (type === 'product') {
                isSelected = state.selections.products.some(p => p.LogicalRef === i.LogicalRef);
            } else if (type === 'main') {
                isSelected = state.selections.main && state.selections.main.LogicalRef === i.LogicalRef;
            } else if (type === 'sub') {
                isSelected = state.selections.sub && state.selections.sub.LogicalRef === i.LogicalRef;
            }
            var selClass = isSelected ? ' border-primary' : '';

            var idAttr = 'data-id="' + i.LogicalRef + '" data-type="' + type + '"',
                baseCard = '<div class="col"><div class="card psm-card' + selClass + '" ' + idAttr + '>',
                bodyOpen = '<div class="card-body text-center">',
                bodyClose = '</div></div></div>',
                html;

            if (type === 'main') {
                html = baseCard +
                    bodyOpen +
                    '<h5 class="psm-main-title">' + i.Name + '</h5>' +
                    bodyClose;
            }
            else if (type === 'sub') {
                html = baseCard +
                    '<img src="' + (i.ImagePath || 'https://via.placeholder.com/150') + '" class="card-img-top">' +
                    bodyOpen +
                    '<h6>' + i.Name + '</h6>' +
                    bodyClose;
            }
            else {
                html = baseCard +
                    '<img src="' + (i.ImagePath || 'https://via.placeholder.com/150') + '" class="card-img-top">' +
                    bodyOpen +
                    '<h6>' + i.Name + '</h6>' +
                    '<button class="btn btn-primary btn-sm mt-2" onclick="showPhotoUploadAreaFromModal(' + i.LogicalRef + ', \'' + i.Name.replace(/'/g, "\\'") + '\', \'' + (i.Code || '').replace(/'/g, "\\'") + '\', \'' + (i.Barcode || '').replace(/'/g, "\\'") + '\')">' +
                    '<i class="fas fa-camera"></i> Fotoğraf Ekle</button>' +
                    bodyClose;
            }

            $(cont).append(html);
        });

        attachCardEvents();
    }




    function attachCardEvents() {
        $('.psm-card').off('click').on('click', function () {
            var type = $(this).data('type');
            var id = $(this).data('id');
            if (type === 'main') {
                state.selections.main = state.data.main.find(function (x) { return x.LogicalRef === id; });
                state.step = 2;
                showStep(2);
                loadSub();
            } else if (type === 'sub') {
                state.selections.sub = state.data.sub.find(function (x) { return x.LogicalRef === id; });
                state.step = 3;
                showStep(3);
                loadProducts();
            } else {
                var prod = state.data.products.find(function (x) { return x.LogicalRef === id; });
                var idx = state.selections.products.findIndex(function (x) { return x.LogicalRef === id; });
                if (idx > -1) {
                    state.selections.products.splice(idx, 1);
                    $(this).removeClass('border-primary');
                } else {
                    state.selections.products.push(prod);
                    $(this).addClass('border-primary');
                }
                updateSummary();
            }
            updateBreadcrumb();
        });
    }

    function showStep(n) {
        $('.psm-step').addClass('d-none');
        $('#psm-step-' + n).removeClass('d-none');
    }

    function updateBreadcrumb() {
        var bc = $('#productSelectionModal .psm-breadcrumb ol.breadcrumb');
        bc.empty();
        var items = [{ text: 'ANAMENÜ', step: 1 }];
        if (state.selections.main) items.push({ text: state.selections.main.Name, step: 2 });
        if (state.selections.sub) items.push({ text: state.selections.sub.Name, step: 3 });
        items.forEach(function (it, idx) {
            if (idx < items.length - 1) {
                bc.append('<li class="breadcrumb-item"><a href="#" data-step="' + it.step + '">' + it.text + '</a></li>');
            } else {
                bc.append('<li class="breadcrumb-item active" aria-current="page">' + it.text + '</li>');
            }
        });
        bc.find('a[data-step]').off('click').on('click', function (e) {
            e.preventDefault();
            var s = +$(this).data('step');
            state.step = s;
            if (s === 1) {
                state.selections.main = null;
                state.selections.sub = null;
                showStep(1);
                render('main', state.data.main);
            } else if (s === 2) {
                state.selections.sub = null;
                showStep(2);
                render('sub', state.data.sub);
            }
            updateSummary();
            updateBreadcrumb();
        });
    }

    function updateSummary() {
        $('#psm-confirm').text('Seç(' + state.selections.products.length + ')');
    }

    $('#psm-reset').on('click', function () {
        state = {
            step: 1,
            selections: { main: null, sub: null, products: [] },
            data: { main: state.data.main, sub: [], products: [] }
        };
        showStep(1);
        render('main', state.data.main);
        updateSummary();
        updateBreadcrumb();
    });

    $('#psm-search').on('input', function () {
        var q = $(this).val().toLocaleLowerCase('tr');
        var list = state.step === 1
            ? state.data.main
            : state.step === 2
                ? state.data.sub
                : state.data.products;

        var filtered = list.filter(function (x) {
            return x.Name.toLocaleLowerCase('tr').includes(q);
        });

        render(
            state.step === 1 ? 'main'
                : state.step === 2 ? 'sub'
                    : 'product',
            filtered
        );
    });


    $('#psm-confirm').on('click', function () {
        $('#productSelectionModal').modal('hide');
        console.log(state.selections.products);
    });

    $('#productSelectionModal')
        .on('show.bs.modal', function () {
            // 1) Tam reset: adımları, seçimleri ve ara verilerini temizle
            state.step = 1;
            state.selections = { main: null, sub: null, products: [] };
            state.data.sub = [];
            state.data.products = [];

            // 2) Arama kutusunu ve summary’i sıfırla
            $('#psm-search').val('');
            updateSummary();

            // 3) Breadcrumb’ı ve görünümü başa al
            showStep(1);
            updateBreadcrumb();

            // 4) Ana kategorileri yükle (veya önceden yüklüyse sadece render et)
            if (!state.data.main.length) {
                loadMain();
            } else {
                render('main', state.data.main);
            }

            // 5) Arama kutusuna focus
            setTimeout(function () { $('#psm-search').focus(); }, 300);
        })
        .on('hidden.bs.modal', function () {
            // eskiden yaptığınız eski hidden reset’i muhafaza edebilir ya da tamamen silebilirsiniz
            $('#psm-search').val('');
            updateSummary();
            state.step = 1;
            showStep(1);
            updateBreadcrumb();
        });

});
