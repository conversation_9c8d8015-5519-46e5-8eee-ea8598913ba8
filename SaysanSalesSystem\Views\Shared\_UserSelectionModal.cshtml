﻿<style>
    :root {
        --psm-primary: #4F46E5;
        --psm-primary-light: #6366F1;
        --psm-secondary: #6B7280;
        --psm-bg: #F8FAFC;
        --psm-card-bg: #FFFFFF;
        --psm-text: #111827;
        --psm-muted: #9CA3AF;
        --psm-border: #E5E7EB;
        --psm-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --psm-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --psm-success: #059669;
        --psm-success-light: #10B981;
        --psm-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .blur-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(8px);
        z-index: 9999;
        display: none;
    }

    .user-selection-modal {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10000;
        display: none;
        width: 70vw;
    }

    .user-selection-modal .card {
        background: var(--psm-card-bg);
        border: none;
        border-radius: 20px;
        box-shadow: var(--psm-shadow-lg);
        overflow: hidden;
    }

    .user-selection-modal .card-header {
        background: var(--psm-gradient);
        color: #FFFFFF;
        padding: 2rem;
        border: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    .user-selection-modal .card-header h3 {
        font-weight: 700;
        font-size: 1.75rem;
        margin-bottom: 0.5rem;
    }

    .user-selection-modal .card-header p {
        font-size: 1rem;
        opacity: 0.9;
    }

    .user-selection-modal .card-body {
        background: var(--psm-bg);
        padding: 2rem;
    }

    .branch-card {
        background: var(--psm-card-bg);
        border: 2px solid transparent;
        border-radius: 20px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        box-shadow: var(--psm-shadow);
        min-height: 200px;
        min-width: 250px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .branch-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, rgba(79, 70, 229, 0.03) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1;
    }

    .branch-card:hover::before {
        opacity: 1;
    }

    .branch-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: var(--psm-shadow-lg);
        border-color: var(--psm-primary-light);
    }

    .branch-card.selected {
        border-color: var(--psm-success) !important;
        background: linear-gradient(135deg, #ECFDF5 0%, #D1FAE5 100%);
        box-shadow: 0 0 0 4px rgba(5, 150, 105, 0.1);
        transform: translateY(-5px);
    }

    .branch-card .card-body {
        position: relative;
        z-index: 2;
        background: transparent;
        border: none;
        padding: 1rem;
    }

    .branch-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: var(--psm-muted);
        transition: all 0.3s ease;
    }

    .branch-card:hover .branch-icon {
        color: var(--psm-primary-light);
        transform: scale(1.1);
    }

    .branch-card.selected .branch-icon {
        color: var(--psm-success);
    }

    .branch-card .card-title {
        font-weight: 600;
        color: var(--psm-text);
        font-size: 1.25rem;
        margin-bottom: 0;
        transition: color 0.3s ease;
    }

    .branch-card:hover .card-title {
        color: var(--psm-primary);
    }

    .branch-card.selected .card-title {
        color: var(--psm-success);
    }

    .loading-spinner {
        display: none;
        color: var(--psm-primary);
    }

    .loading-spinner .spinner-border {
        width: 3rem;
        height: 3rem;
        border-width: 0.3rem;
        color: var(--psm-primary);
    }

    .loading-spinner p {
        color: var(--psm-muted);
        font-size: 0.95rem;
        margin-top: 1rem;
    }

    .user-info-badge {
        background: var(--psm-gradient);
        color: white;
        padding: 8px 15px;
        border-radius: 25px;
        font-size: 0.85rem;
        font-weight: 600;
        box-shadow: var(--psm-shadow);
        border: none;
        transition: all 0.3s ease;
    }

    .user-info-badge:hover {
        transform: translateY(-1px);
        box-shadow: var(--psm-shadow-lg);
    }

    @@media (max-width: 767.98px) {
        .user-selection-modal {
            width: 90vw;
        }

        .user-selection-modal .card-header {
            padding: 1.5rem 1rem;
        }

        .user-selection-modal .card-body {
            padding: 1.5rem 1rem;
        }

        .branch-card {
            min-width: 100%;
            min-height: 150px;
            padding: 1rem;
        }

        .branch-icon {
            font-size: 3rem;
        }

        .branch-card .card-title {
            font-size: 1.1rem;
        }
    }

    @@media (max-width: 575.98px) {
        .user-selection-modal {
            width: 95vw;
        }

        .user-selection-modal .card-header {
            padding: 1rem;
        }

        .user-selection-modal .card-header h3 {
            font-size: 1.5rem;
        }

        .user-selection-modal .card-body {
            padding: 1rem;
        }

        .branch-card {
            min-height: 120px;
        }

        .branch-icon {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
    }

    .branch-card {
        animation: fadeIn 0.5s ease-in-out;
    }

    @@keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

<div id="blurOverlay" class="blur-overlay"></div>
<div id="userSelectionModal" class="user-selection-modal">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12 col-lg-">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-building me-3"></i>
                            Şube Seçimi
                        </h3>
                        <p class="mb-0 mt-2 opacity-75">Çalıştığınız şubeyi seçerek devam edin</p>
                    </div>
                    <div class="card-body p-5">
                        <div class="row g-4" id="branchContainer">
                            <!-- Branch cards will be injected here dynamically -->
                        </div>
                        <div class="text-center mt-4">
                            <div class="loading-spinner">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Yükleniyor...</span>
                                </div>
                                <p class="mt-3 text-muted">Şube bilgileri yükleniyor...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>