﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SaysanSalesSystem.Models
{
    public class DataTableModel
    {
        public class DataTableRequest
        {
            public int draw { get; set; }
            public int start { get; set; }
            public int length { get; set; }
            public Search search { get; set; }
            public List<Order> order { get; set; }
            public List<Column> columns { get; set; }
            public string customSearch { get; set; } // Özel arama için
        }
        public class Search
        {
            public string value { get; set; }
            public bool regex { get; set; }
        }

        public class Order
        {
            public int column { get; set; }
            public string dir { get; set; }
        }

        public class Column
        {
            public string data { get; set; }
            public string name { get; set; }
            public bool searchable { get; set; }
            public bool orderable { get; set; }
            public Search search { get; set; }
        }
        

    }
}