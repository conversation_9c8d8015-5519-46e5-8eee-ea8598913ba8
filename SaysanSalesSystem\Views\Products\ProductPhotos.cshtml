@{
    ViewBag.Title = "ProductPhotos";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<style>
    html, body {
        height: 100%;
        margin: 0;
        background: #f8f9fa;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .invoice-form-wide {
        width: 100%;
        margin: 0;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 32px;
        min-height: 100vh;
    }

    .custom-table-frame {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        box-shadow: none;
        background: #ffffff;
        padding: 24px;
    }

    h2 {
        color: #212529;
        font-weight: 600;
        font-size: 24px;
        margin-bottom: 32px;
    }

    h5 {
        color: #495057;
        font-weight: 500;
        font-size: 16px;
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 500;
        color: #374151;
        margin-bottom: 6px;
        font-size: 14px;
    }

    .form-control, .modern-select {
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 10px 12px;
        font-size: 14px;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        background: #ffffff;
    }

    .form-control:focus, .modern-select:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        outline: none;
    }

    .btn {
        padding: 10px 16px;
        font-weight: 500;
        border-radius: 6px;
        font-size: 14px;
        border: none;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        display: inline-flex;
        align-items: center;
        gap: 6px;
    }

    .btn-primary {
        background-color: #3b82f6;
        color: white;
    }

    .btn-primary:hover {
        background-color: #2563eb;
        color: white;
    }

    .btn-success {
        background-color: #10b981;
        color: white;
    }

    .btn-success:hover {
        background-color: #059669;
        color: white;
    }

    .btn-outline-secondary {
        background-color: #f9fafb;
        color: #6b7280;
        border: 1px solid #d1d5db;
    }

    .btn-outline-secondary:hover {
        background-color: #f3f4f6;
        color: #374151;
        border-color: #9ca3af;
    }

    #photoUploadArea {
        margin-bottom: 2rem;
    }

    .photo-upload-card {
        background: #ffffff;
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .photo-upload-card.active {
        border-color: #3b82f6;
        background: #f0f9ff;
    }

    .upload-progress {
        margin-top: 1rem;
    }

    .photo-preview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .photo-preview-item {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        aspect-ratio: 1;
    }

    .photo-preview-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .photo-preview-item .remove-btn {
        position: absolute;
        top: 4px;
        right: 4px;
        background: rgba(239, 68, 68, 0.9);
        color: white;
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        cursor: pointer;
    }

    /* Responsive */
    @@media (max-width: 768px) {
        .invoice-form-wide {
            padding: 20px;
            border-radius: 0;
        }

        h2 {
            font-size: 20px;
            text-align: center;
        }

        .btn {
            width: 100%;
            justify-content: center;
        }
    }

</style>

<div class="container-fluid mt-4 px-0">
    <div class="invoice-form-wide">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Ürün Fotoğraf Yönetimi</h2>
        </div>

        <div id="photoUploadArea"></div>

        <div class="custom-table-frame">
            <h5>Ürünler</h5>
            <div id="productsArea">
                <!-- Ürünler buraya yüklenecek -->
            </div>
        </div>
    </div>
</div>

@Html.Partial("_ProductSelectionModal")



@section scripts {
    <script src="~/Content/Scripts/product-selector.js"></script>
    <script>
        $(document).ready(function () {
            // Sayfa açıldığında modal'ı aç
            showProductModal();

            $('#psm-confirm').on('click', function () {
                var selections = ProductSelector.getSelections();
                var logicalRefs = selections.map(function (item) {
                    return item.LogicalRef;
                });

                $.ajax({
                    url: '/Invoice/GetSelectedItems',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ logicalRefs: logicalRefs }),
                    success: function (response) {
                        if (response.success) {
                            displayProductsWithPhotoButtons(response.data);
                        } else {
                            alert("Ürünler getirilemedi.");
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('Ürünleri çekerken hata:', error);
                    }
                });
            });
        });

        function displayProductsWithPhotoButtons(products) {
            var html = '<div class="row">';

            products.forEach(function(product) {
                html += `
                    <div class="col-md-3 col-sm-6 col-lg-3 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="position-relative" style="height: 200px;">
                                <img src="${product.ImagePath || 'https://via.placeholder.com/200'}"
                                     class="card-img-top h-100 w-100" style="object-fit: cover;" />
                            </div>
                            <div class="card-body d-flex flex-column justify-content-between">
                                <div>
                                    <h5 class="card-title">${product.Name}</h5>
                                    <p class="card-text text-muted">Kod: ${product.Code || 'N/A'}</p>
                                    <p class="card-text text-muted">Barkod: ${product.Barcode || 'N/A'}</p>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-primary btn-sm w-100"
                                            onclick="showPhotoUploadArea(${product.LogicalRef}, '${product.Name.replace(/'/g, "\\'")}', '${product.Code || ''}', '${product.Barcode || ''}')">
                                        <i class="fas fa-camera"></i> Fotoğraf Ekle
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            $('#productsArea').html(html);
        }

        function showPhotoUploadAreaFromModal(itemId, itemName, itemCode, itemBarcode) {
            // Modal'ı kapat
            $('#productSelectionModal').modal('hide');
            // Fotoğraf ekleme alanını göster
            showPhotoUploadArea(itemId, itemName, itemCode, itemBarcode);
        }

        function showPhotoUploadArea(itemId, itemName, itemCode, itemBarcode) {
            const area = $("#photoUploadArea");

            area.html(`
                <div class="photo-upload-card active">
                    <div class="mb-3">
                        <h4 class="text-primary">
                            <i class="fas fa-camera me-2"></i>
                            ${itemName} - Fotoğraf Yükle
                        </h4>
                        <p class="text-muted mb-0">Ürün Kodu: ${itemCode || 'N/A'}</p>
                        <p class="text-muted">Barkod: ${itemBarcode || 'N/A'}</p>
                    </div>

                    <div class="mb-3">
                        <input type="file" id="photoFiles" class="form-control" accept="image/*" multiple
                               onchange="previewPhotos()" />
                        <small class="text-muted">Birden fazla fotoğraf seçebilirsiniz</small>
                    </div>

                    <div id="photoPreviews" class="photo-preview-grid"></div>

                    <div class="d-flex justify-content-center gap-2 mt-3">
                        <button class="btn btn-primary" id="savePhoto"
                            onclick="uploadPhotos(${itemId})">
                            <i class="fas fa-upload"></i> Fotoğrafları Kaydet
                        </button>
                        <button class="btn btn-secondary" onclick="clearUploadArea()">
                            <i class="fas fa-times"></i> İptal
                        </button>
                    </div>

                    <div class="progress upload-progress d-none" id="uploadProgress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            `);

            // Fotoğraf ekleme alanına scroll
            area[0].scrollIntoView({ behavior: 'smooth' });
        }

        function clearUploadArea() {
            $("#photoUploadArea").empty();
        }

        function previewPhotos() {
            const files = $("#photoFiles")[0].files;
            const previewContainer = $("#photoPreviews");
            previewContainer.empty();

            if (files.length === 0) return;

            for (let i = 0; i < files.length; i++) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    previewContainer.append(`
                        <div class="photo-preview-item">
                            <img src="${e.target.result}" alt="Preview">
                            <button type="button" class="remove-btn" onclick="removePreview(this)">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `);
                };
                reader.readAsDataURL(files[i]);
            }
        }

        function removePreview(button) {
            $(button).closest('.photo-preview-item').remove();
        }

        function uploadPhotos(itemId) {
            const files = $("#photoFiles")[0].files;
            if (files.length === 0) {
                alert("Lütfen en az bir fotoğraf seçin.");
                return;
            }

            const formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append("photos", files[i]);
            }
            formData.append("itemId", itemId);

            const progressBar = $("#uploadProgress .progress-bar");
            const progressContainer = $("#uploadProgress");
            progressContainer.removeClass("d-none");

            $.ajax({
                url: '/Products/UploadPhotos',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function () {
                    const xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener("progress", function (evt) {
                        if (evt.lengthComputable) {
                            const percentComplete = (evt.loaded / evt.total) * 100;
                            progressBar.css("width", percentComplete + "%");
                        }
                    }, false);
                    return xhr;
                },
                success: function (result) {
                    if (result.success == true) {
                        progressBar.css("width", "100%");
                        setTimeout(() => {
                            alert(result.message + " Fotoğraf başarıyla kaydedildi.");
                            clearUploadArea();
                            // Modal'ı tekrar aç
                            showProductModal();
                        }, 500);
                    }
                    else {
                        progressBar.css("width", "0%");
                        progressContainer.addClass("bg-danger");
                        alert("Fotoğraflar yüklenirken bir hata oluştu: " + result.message);
                    }
                },
                error: function (xhr, status, error) {
                    console.error(error);
                    progressBar.css("width", "0%");
                    progressContainer.addClass("bg-danger");
                    alert("Fotoğraflar yüklenirken bir hata oluştu: " + error);
                }
            });
        }

        function clearUploadArea() {
            $("#photoUploadArea").empty();
        }

    </script>
}
