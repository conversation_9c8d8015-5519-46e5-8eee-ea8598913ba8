@{
    ViewBag.Title = "ProductPhotos";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    .page-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 3rem;
    }

    .group-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

        .group-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #007bff;
        }

    .group-icon {
        font-size: 2.5rem;
        color: #007bff;
        margin-bottom: 1rem;
    }

    .group-title {
        font-weight: 500;
        color: #343a40;
        margin: 0;
    }

    .loading {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .card {
        border-radius: 10px;
        transition: transform 0.2s;
    }

    /*  .card:hover {
                transform: scale(1.05);
            }
    */
    .hidden {
        display: none !important;
    }

    .photo-gallery {
        position: relative;
    }


        .photo-gallery button {
            font-size: 12px;
            padding: 2px 5px;
        }

    .card-img-top {
        border-bottom: 1px solid #eee;
    }

    .photo-gallery img {
        height: 100px;
        object-fit: cover;
        border: 1px solid #ccc;
        border-radius: 5px;
    }

    .photo-gallery .position-relative {
        display: inline-block;
    }

    #savePhoto {
        background: var(--psm-gradient);
        color: white;
    }
</style>

<div id="photoUploadArea"></div>

<div class=" py-5">
    <h1 class="text-center page-title" id="title"></h1>
    <div id="list"></div>
</div>


<script src="https://code.jquery.com/jquery-3.7.1.js"></script>

<script>

    $(document).ready(function () {
        MainGroupList();
    });

    function MainGroupList() {
        $.ajax({
            type: "Get",
            url: "/Products/MainGroupList",
            dataType: 'html',
            beforeSend: function () {
                $('#list').html("<div align='center'><br/><br/><br/>Yükleniyor...</div>");
            },
            success: function (data) {
                $("#list").html(data);
            }
        })

        $("#title").text("Ürün Grupları");
    }

    function SubGroupList(MainGroup) {
        $.ajax({
            type: "Get",
            url: "/Products/SubGroupList?MainGroup=" + MainGroup,
            dataType: 'html',
            beforeSend: function () {
                $('#list').html("<div align='center'><br/><br/><br/>Yükleniyor...</div>");
            },
            success: function (data) {
                $("#list").html(data);
            }
        })

        $("#title").text(MainGroup + " Alt Grupları");

    }

    function ItemList(mainGroup, subGroup) {
        $.ajax({
            type: "Get",
            url: "/Products/ItemList?MainGroup=" + mainGroup + "&SubGroup=" + subGroup,
            dataType: 'html',
            beforeSend: function () {
                $('#list').html("<div align='center'><br/><br/><br/>Yükleniyor...</div>");
            },
            success: function (data) {
                $("#list").html(data);
            }
        });

        $("#title").text(subGroup + " / Ürünler");

    }

    //function filterItems() {
    //    const input = document.getElementById('searchInput');
    //    const filter = input.value.toLocaleLowerCase('tr-TR');
    //    const ul = document.getElementById("itemList");
    //    const liList = ul.getElementsByTagName('li');

    //    for (let i = 0; i < liList.length; i++) {
    //        const li = liList[i];
    //        const h5 = li.querySelector("h5");
    //        const p = li.querySelector("p");
    //        console.log(li);

    //        if (h5 && p) {
    //            const nameValue = h5.textContent.toLocaleLowerCase('tr-TR');
    //            const barcodeValue = p.textContent.toLowerCase();

    //            if (nameValue.includes(filter) || barcodeValue.includes(filter)) {
    //                li.classList.remove("hidden");
    //            } else {
    //                li.classList.add("hidden");
    //            }
    //        }
    //    }
    //}

    function filterItems() {
        const input = document.getElementById('searchInput');
        const filter = input.value.toLocaleLowerCase('tr-TR');
        const container = document.getElementById("itemList");
        const itemCards = container.querySelectorAll('[id^="item-"]');

        itemCards.forEach(card => {
            const title = card.querySelector(".card-title");
            const barcode = card.querySelector(".card-text");

            const nameValue = title ? title.textContent.toLocaleLowerCase('tr-TR') : '';
            const barcodeValue = barcode ? barcode.textContent.toLowerCase() : '';

            if (nameValue.includes(filter) || barcodeValue.includes(filter)) {
                card.style.display = "block";
            } else {
                card.style.display = "none";
            }
        });
    }


    function addPhoto(itemId, itemName, maingroup, subgroup) {
        const area = $("#photoUploadArea");

        console.log(maingroup, subgroup);
        const escapedMainGroup = maingroup.replace(/'/g, "\\'");
        const escapedSubGroup = subgroup.replace(/'/g, "\\'");

        area.html(`
    <div class="card mb-3 shadow-sm">
        <div class="card-body">
            <h5 class="card-title">${itemName} - Fotoğraf Yükle</h5>

            <div class="mb-3">
                <input type="file" id="photoFiles" class="form-control" accept="image/*"
                       onchange="previewPhotos()" />
            </div>

            <div id="photoPreviews" class="row mb-3"></div>

            <div class="d-flex">
                <button class="btn btn-primary" id="savePhoto"
                    onclick="uploadPhotos(${itemId}, '${escapedMainGroup}', '${escapedSubGroup}')">
                    <i class="fas fa-upload"></i> Fotoğrafları Kaydet
                </button>
                <button class="btn btn-secondary ms-2" onclick="clearUploadArea()">
                    İptal
                </button>
            </div>

            <div class="progress mt-3 d-none" id="uploadProgress">
                <div class="progress-bar progress-bar-striped progress-bar-animated"
                     role="progressbar" style="width: 0%"></div>
            </div>
        </div>
    </div>
`);

    }

    function clearUploadArea() {
        $("#photoUploadArea").empty();
    }

    function previewPhotos() {
        const files = $("#photoFiles")[0].files;
        const previewContainer = $("#photoPreviews");
        previewContainer.empty();

        if (files.length === 0) return;

        previewContainer.append(`
        <div class="col-12 mb-2">
            <small class="text-muted">${files.length} fotoğraf seçildi</small>
        </div>
    `);

        const maxPreviews = 10; // önizleme 10 fotoğraf
        for (let i = 0; i < Math.min(files.length, maxPreviews); i++) {
            const reader = new FileReader();
            reader.onload = function (e) {
                previewContainer.append(`
                <div class="col-md-2 col-4 mb-2">
                    <img src="${e.target.result}" class="img-thumbnail" style="height: 100px; object-fit: cover;">
                    <small class="d-block text-center">${files[i].name}</small>
                </div>
            `);
            };
            reader.readAsDataURL(files[i]);
        }

        if (files.length > maxPreviews) {
            previewContainer.append(`
            <div class="col-12">
                <small class="text-muted">+ ${files.length - maxPreviews} daha fazla</small>
            </div>
        `);
        }
    }

    function uploadPhotos(itemId, maingroup, subgroup) {
        const files = $("#photoFiles")[0].files;
        if (files.length === 0) {
            alert("Lütfen en az bir fotoğraf seçin.");
            return;
        }

        const formData = new FormData();
        for (let i = 0; i < files.length; i++) {
            formData.append("photos", files[i]);
        }
        formData.append("itemId", itemId);

        const progressBar = $("#uploadProgress .progress-bar");
        const progressContainer = $("#uploadProgress");
        progressContainer.removeClass("d-none");

        $.ajax({
            url: '/Products/UploadPhotos',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function () {
                const xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener("progress", function (evt) {
                    if (evt.lengthComputable) {
                        const percentComplete = (evt.loaded / evt.total) * 100;
                        progressBar.css("width", percentComplete + "%");
                    }
                }, false);
                return xhr;
            },
            success: function (result) {

                if (result.success == true) {
                    progressBar.css("width", "100%");
                    setTimeout(() => {
                        alert(result.message + " Fotoğraf başarıyla kaydedildi.");
                        clearUploadArea();
                    }, 500);
                }
                else {
                    progressBar.css("width", "0%");
                    progressContainer.addClass("bg-danger");
                    alert("Fotoğraflar yüklenirken bir hata oluştu: " + error);
                }
            },
            error: function (xhr, status, error) {
                console.error(error);
                progressBar.css("width", "0%");
                progressContainer.addClass("bg-danger");
                alert("Fotoğraflar yüklenirken bir hata oluştu: " + error);
            }
        });

        //setTimeout(() => {
        //    togglePhotos(itemId); // kapatıp açma
        //    togglePhotos(itemId);
        //}, 500);
        ItemList(maingroup, subgroup);

    }

    //function togglePhotos(itemId) {
    //    var container = $("#photos-" + itemId);
    //    var gallery = container.find(".photo-gallery");

    //    if (container.hasClass("d-none")) {
    //        $.ajax({
    //            url: "/Products/GetItemPhotos",
    //            type: "post",
    //            data: { itemId: itemId },
    //            success: function (photos) {
    //                gallery.empty();
    //                if (photos.length === 0) {
    //                    gallery.append('<p class="text-muted">Fotoğraf bulunamadı.</p>');
    //                } else {
    //                    for (var i = 0; i < photos.length; i++) {
    //                        var photo = photos[i];
    //                        var photoHtml = `
    //                        <div class="position-relative me-2 mb-2">
    //                            <img src="${photo.ImagePath}" class="rounded border" style="height: 150px;" alt="${photo.ImageName}" />
    //                            <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1" onclick="deleteItemPhoto(${photo.Id}, ${itemId})">
    //                                <i class="fas fa-times"></i>
    //                            </button>
    //                        </div>`;
    //                        gallery.append(photoHtml);
    //                    }
    //                }
    //                container.removeClass("d-none");
    //            },
    //            error: function (xhr, status, error) {
    //                alert("Fotoğraflar alınırken hata oluştu: " + error);
    //            }
    //        });
    //    } else {
    //        container.addClass("d-none");
    //    }
    //}

    function deleteItemPhoto(itemId) {
        if (!confirm("Bu fotoğrafı silmek istediğinize emin misiniz?")) return;

        $.ajax({
            url: "/Products/DeleteItemPhoto",
            type: "POST",
            data: { id: itemId },
            success: function (response) {
                if (response.success) {
                    alert("Fotoğraf silindi.");
                    //togglePhotos(itemId);
                    //togglePhotos(itemId);
                } else {
                    alert("Silme başarısız: " + response.message);
                }
            },
            error: function (xhr, status, error) {
                alert("Silme sırasında hata oluştu: " + error);
            }
        });
    }




</script>
