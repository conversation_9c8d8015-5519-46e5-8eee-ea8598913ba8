@{
    ViewBag.Title = "ProductPhotos";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<style>
    :root {
        --psm-primary: #4F46E5;
        --psm-primary-light: #6366F1;
        --psm-secondary: #6B7280;
        --psm-bg: #F8FAFC;
        --psm-card-bg: #FFFFFF;
        --psm-text: #111827;
        --psm-muted: #9CA3AF;
        --psm-border: #E5E7EB;
        --psm-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --psm-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --psm-success: #059669;
        --psm-success-light: #10B981;
        --psm-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    html, body {
        height: 100%;
        margin: 0;
        background: var(--psm-bg);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        color: var(--psm-text);
    }

    .invoice-form-wide {
        width: 100%;
        margin: 0;
        background: var(--psm-card-bg);
        border-radius: 8px;
        box-shadow: var(--psm-shadow);
        padding: 32px;
        min-height: 100vh;
    }

    .psm-header {
        padding: 1.5rem 2rem;
        background: var(--psm-gradient);
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 8px 8px 0 0;
        margin: -32px -32px 2rem -32px;
        backdrop-filter: blur(10px);
    }

    .psm-header #mainSearch {
        flex: 1;
        max-width: 450px;
        margin: 0 1.5rem;
        border-radius: 16px;
        border: 2px solid rgba(255, 255, 255, 0.2);
        padding: 0.75rem 1.25rem;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        color: #FFFFFF;
        font-size: 0.95rem;
        transition: all 0.3s ease;
    }

    .psm-header #mainSearch::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .psm-header #mainSearch:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.25);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    }

    .psm-body {
        padding: 2rem 0;
        background: var(--psm-bg);
    }

    .psm-body .row {
        display: grid;
        grid-template-columns: repeat(auto-fit, 320px);
        justify-content: center;
        gap: 1.5rem;
        margin: 0;
    }

    .psm-card {
        width: 320px;
        background: var(--psm-card-bg);
        border: 2px solid transparent;
        border-radius: 20px;
        padding: 1.25rem;
        text-align: center;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        box-shadow: var(--psm-shadow);
    }

    .psm-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, rgba(79, 70, 229, 0.03) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1;
    }

    .psm-card:hover::before {
        opacity: 1;
    }

    .psm-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: var(--psm-shadow-lg);
        border-color: var(--psm-primary-light);
    }

    .psm-card img {
        width: 280px;
        height: 280px;
        object-fit: cover;
        display: block;
        margin: 0 auto 1rem auto;
        border-radius: 16px;
        position: relative;
        z-index: 2;
        transition: transform 0.3s ease;
    }

    .psm-card:hover img {
        transform: scale(1.05);
    }

    .psm-card h5,
    .psm-card h6 {
        position: relative;
        z-index: 2;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--psm-text);
    }

    .psm-main-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin: 2rem 0;
        color: var(--psm-text);
        text-align: center;
    }

    .breadcrumb {
        background: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: var(--psm-muted);
        padding: 0 0.75rem;
        font-size: 1.1rem;
    }

    .breadcrumb-item a {
        color: var(--psm-primary);
        text-decoration: none;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        transition: background-color 0.2s ease;
    }

    .breadcrumb-item a:hover {
        background: rgba(79, 70, 229, 0.1);
    }

    .breadcrumb-item.active {
        color: var(--psm-muted);
        font-weight: 500;
    }

    .btn {
        padding: 10px 16px;
        font-weight: 500;
        border-radius: 6px;
        font-size: 14px;
        border: none;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        display: inline-flex;
        align-items: center;
        gap: 6px;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--psm-primary) 0%, var(--psm-primary-light) 100%);
        color: white;
        box-shadow: var(--psm-shadow);
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: var(--psm-shadow-lg);
        color: white;
    }

    #photoUploadArea {
        margin-bottom: 2rem;
    }

    .photo-upload-card {
        background: #ffffff;
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .photo-upload-card.active {
        border-color: #3b82f6;
        background: #f0f9ff;
    }

    .upload-progress {
        margin-top: 1rem;
    }

    .photo-preview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .photo-preview-item {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        aspect-ratio: 1;
    }

    .photo-preview-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .photo-preview-item .remove-btn {
        position: absolute;
        top: 4px;
        right: 4px;
        background: rgba(239, 68, 68, 0.9);
        color: white;
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        cursor: pointer;
    }

    /* Responsive */
    @@media (max-width: 768px) {
        .invoice-form-wide {
            padding: 20px;
            border-radius: 0;
        }

        h2 {
            font-size: 20px;
            text-align: center;
        }

        .btn {
            width: 100%;
            justify-content: center;
        }
    }

</style>

<div class="container-fluid mt-4 px-0">
    <div class="invoice-form-wide">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Ürün Fotoğraf Yönetimi</h2>
        </div>

        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb" id="mainBreadcrumb">
                <li class="breadcrumb-item active">Ana Kategoriler</li>
            </ol>
        </nav>

        <!-- Arama -->
        <div class="mb-4">
            <input type="search" class="form-control" placeholder="Kategori/Ürün ara..." id="mainSearch">
        </div>

        <!-- Fotoğraf Ekleme Alanı -->
        <div id="photoUploadArea"></div>

        <!-- İçerik Alanı -->
        <div id="contentArea">
            <!-- Ana kategoriler, alt kategoriler ve ürünler buraya yüklenecek -->
        </div>

    </div>
</div>



@section scripts {
    <script>
        var currentStep = 1; // 1: Ana kategoriler, 2: Alt kategoriler, 3: Ürünler
        var selectedMain = null;
        var selectedSub = null;

        $(document).ready(function () {
            loadMainCategories();

            // Arama işlevi
            $('#mainSearch').on('input', function() {
                var searchTerm = $(this).val().toLowerCase();
                filterContent(searchTerm);
            });
        });

        function loadMainCategories() {
            $.ajax({
                url: '/SelectProduct/GetMainCategories',
                method: 'GET',
                dataType: 'json',
                beforeSend: function () {
                    $('#contentArea').html('<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Yükleniyor...</span></div></div>');
                },
                success: function (response) {
                    if (response.success) {
                        renderMainCategories(response.data);
                        updateBreadcrumb();
                    }
                },
                error: function () {
                    $('#contentArea').html('<div class="alert alert-danger">Ana kategoriler yüklenirken hata oluştu.</div>');
                }
            });
        }

        function renderMainCategories(categories) {
            var html = '<div class="row">';
            categories.forEach(function(category) {
                html += `
                    <div class="col">
                        <div class="card psm-card" onclick="selectMainCategory('${category.MainGroup}', '${category.Name}')">
                            <img src="${category.ImagePath || 'https://via.placeholder.com/150'}" class="card-img-top">
                            <div class="card-body text-center">
                                <h5 class="psm-main-title">${category.Name}</h5>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            $('#contentArea').html(html);
        }

        function selectMainCategory(mainGroup, mainName) {
            selectedMain = { MainGroup: mainGroup, Name: mainName };
            currentStep = 2;
            loadSubCategories(mainGroup);
        }

        function loadSubCategories(mainGroup) {
            $.ajax({
                url: '/SelectProduct/GetSubCategories',
                method: 'GET',
                dataType: 'json',
                data: { main: mainGroup },
                beforeSend: function () {
                    $('#contentArea').html('<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Yükleniyor...</span></div></div>');
                },
                success: function (response) {
                    if (response.success) {
                        renderSubCategories(response.data);
                        updateBreadcrumb();
                    }
                },
                error: function () {
                    $('#contentArea').html('<div class="alert alert-danger">Alt kategoriler yüklenirken hata oluştu.</div>');
                }
            });
        }

        function renderSubCategories(categories) {
            var html = '<div class="row">';
            categories.forEach(function(category) {
                html += `
                    <div class="col">
                        <div class="card psm-card" onclick="selectSubCategory('${category.SubGroup}', '${category.Name}')">
                            <img src="${category.ImagePath || 'https://via.placeholder.com/150'}" class="card-img-top">
                            <div class="card-body text-center">
                                <h6>${category.Name}</h6>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            $('#contentArea').html(html);
        }

        function selectSubCategory(subGroup, subName) {
            selectedSub = { SubGroup: subGroup, Name: subName };
            currentStep = 3;
            loadProducts(selectedMain.MainGroup, subGroup);
        }

        function loadProducts(mainGroup, subGroup) {
            $.ajax({
                url: '/SelectProduct/GetProducts',
                method: 'GET',
                dataType: 'json',
                data: { main: mainGroup, sub: subGroup },
                beforeSend: function () {
                    $('#contentArea').html('<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Yükleniyor...</span></div></div>');
                },
                success: function (response) {
                    if (response.success) {
                        renderProducts(response.data);
                        updateBreadcrumb();
                    }
                },
                error: function () {
                    $('#contentArea').html('<div class="alert alert-danger">Ürünler yüklenirken hata oluştu.</div>');
                }
            });
        }

        function renderProducts(products) {
            var html = '<div class="row">';
            products.forEach(function(product) {
                html += `
                    <div class="col-md-3 col-sm-6 col-lg-3 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="position-relative" style="height: 200px;">
                                <img src="${product.ImagePath || 'https://via.placeholder.com/200'}"
                                     class="card-img-top h-100 w-100" style="object-fit: cover;" />
                            </div>
                            <div class="card-body d-flex flex-column justify-content-between">
                                <div>
                                    <h5 class="card-title">${product.Name}</h5>
                                    <p class="card-text text-muted">Kod: ${product.Code || 'N/A'}</p>
                                    <p class="card-text text-muted">Barkod: ${product.Barcode || 'N/A'}</p>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-primary btn-sm w-100"
                                            onclick="showPhotoUploadArea(${product.LogicalRef}, '${product.Name.replace(/'/g, "\\'")}', '${product.Code || ''}', '${product.Barcode || ''}')">
                                        <i class="fas fa-camera"></i> Fotoğraf Ekle
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            $('#contentArea').html(html);
        }
        window.showPhotoUploadAreaFromModal = function(itemId, itemName, itemCode, itemBarcode) {
            // Modal'ı kapat
            $('#productSelectionModal').modal('hide');
            // Fotoğraf ekleme alanını göster
            showPhotoUploadArea(itemId, itemName, itemCode, itemBarcode);
        };

        window.showPhotoUploadArea = function(itemId, itemName, itemCode, itemBarcode) {
            const area = $("#photoUploadArea");

            area.html(`
                <div class="photo-upload-card active">
                    <div class="mb-3">
                        <h4 class="text-primary">
                            <i class="fas fa-camera me-2"></i>
                            ${itemName} - Fotoğraf Yükle
                        </h4>
                        <p class="text-muted mb-0">Ürün Kodu: ${itemCode || 'N/A'}</p>
                        <p class="text-muted">Barkod: ${itemBarcode || 'N/A'}</p>
                    </div>

                    <div class="mb-3">
                        <input type="file" id="photoFiles" class="form-control" accept="image/*" multiple
                               onchange="previewPhotos()" />
                        <small class="text-muted">Birden fazla fotoğraf seçebilirsiniz</small>
                    </div>

                    <div id="photoPreviews" class="photo-preview-grid"></div>

                    <div class="d-flex justify-content-center gap-2 mt-3">
                        <button class="btn btn-primary" id="savePhoto"
                            onclick="uploadPhotos(${itemId})">
                            <i class="fas fa-upload"></i> Fotoğrafları Kaydet
                        </button>
                        <button class="btn btn-secondary" onclick="clearUploadArea()">
                            <i class="fas fa-times"></i> İptal
                        </button>
                    </div>

                    <div class="progress upload-progress d-none" id="uploadProgress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            `);

            // Fotoğraf ekleme alanına scroll
            area[0].scrollIntoView({ behavior: 'smooth' });
        };

        window.clearUploadArea = function() {
            $("#photoUploadArea").empty();
        };

        window.previewPhotos = function() {
            const files = $("#photoFiles")[0].files;
            const previewContainer = $("#photoPreviews");
            previewContainer.empty();

            if (files.length === 0) return;

            for (let i = 0; i < files.length; i++) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    previewContainer.append(`
                        <div class="photo-preview-item">
                            <img src="${e.target.result}" alt="Preview">
                            <button type="button" class="remove-btn" onclick="removePreview(this)">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `);
                };
                reader.readAsDataURL(files[i]);
            }
        };

        window.removePreview = function(button) {
            $(button).closest('.photo-preview-item').remove();
        };

        window.uploadPhotos = function(itemId) {
            const files = $("#photoFiles")[0].files;
            if (files.length === 0) {
                alert("Lütfen en az bir fotoğraf seçin.");
                return;
            }

            const formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append("photos", files[i]);
            }
            formData.append("itemId", itemId);

            const progressBar = $("#uploadProgress .progress-bar");
            const progressContainer = $("#uploadProgress");
            progressContainer.removeClass("d-none");

            $.ajax({
                url: '/Products/UploadPhotos',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function () {
                    const xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener("progress", function (evt) {
                        if (evt.lengthComputable) {
                            const percentComplete = (evt.loaded / evt.total) * 100;
                            progressBar.css("width", percentComplete + "%");
                        }
                    }, false);
                    return xhr;
                },
                success: function (result) {
                    if (result.success == true) {
                        progressBar.css("width", "100%");
                        setTimeout(() => {
                            alert(result.message + " Fotoğraf başarıyla kaydedildi.");
                            clearUploadArea();
                            // Modal'ı tekrar aç
                            showProductModal();
                        }, 500);
                    }
                    else {
                        progressBar.css("width", "0%");
                        progressContainer.addClass("bg-danger");
                        alert("Fotoğraflar yüklenirken bir hata oluştu: " + result.message);
                    }
                },
                error: function (xhr, status, error) {
                    console.error(error);
                    progressBar.css("width", "0%");
                    progressContainer.addClass("bg-danger");
                    alert("Fotoğraflar yüklenirken bir hata oluştu: " + error);
                }
            });
        };

    </script>
}
