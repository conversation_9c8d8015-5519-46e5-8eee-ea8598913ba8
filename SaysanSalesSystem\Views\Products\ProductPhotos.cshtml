@{
    ViewBag.Title = "ProductPhotos";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

<style>
    html, body {
        height: 100%;
        margin: 0;
        background: #f8f9fa;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .photo-form-wide {
        width: 100%;
        margin: 0;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 32px;
        min-height: 100vh;
    }

    h2 {
        color: #212529;
        font-weight: 600;
        font-size: 24px;
        margin-bottom: 32px;
    }

    .btn {
        padding: 10px 16px;
        font-weight: 500;
        border-radius: 6px;
        font-size: 14px;
        border: none;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        display: inline-flex;
        align-items: center;
        gap: 6px;
    }

    .btn-primary {
        background-color: #3b82f6;
        color: white;
    }

    .btn-primary:hover {
        background-color: #2563eb;
        color: white;
    }

    .page-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 3rem;
    }

    .group-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

        .group-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #007bff;
        }

    .group-icon {
        font-size: 2.5rem;
        color: #007bff;
        margin-bottom: 1rem;
    }

    .group-title {
        font-weight: 500;
        color: #343a40;
        margin: 0;
    }

    .loading {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .card {
        border-radius: 10px;
        transition: transform 0.2s;
    }

    .hidden {
        display: none !important;
    }

    .photo-gallery {
        position: relative;
    }

        .photo-gallery button {
            font-size: 12px;
            padding: 2px 5px;
        }

    .card-img-top {
        border-bottom: 1px solid #eee;
    }

    .photo-gallery img {
        height: 100px;
        object-fit: cover;
        border: 1px solid #ccc;
        border-radius: 5px;
    }

    .photo-gallery .position-relative {
        display: inline-block;
    }

    #photoUploadArea {
        margin-bottom: 2rem;
    }

    .photo-upload-card {
        background: #ffffff;
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .photo-upload-card.active {
        border-color: #3b82f6;
        background: #f0f9ff;
    }

    .upload-progress {
        margin-top: 1rem;
    }

    .photo-preview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .photo-preview-item {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        aspect-ratio: 1;
    }

    .photo-preview-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .photo-preview-item .remove-btn {
        position: absolute;
        top: 4px;
        right: 4px;
        background: rgba(239, 68, 68, 0.9);
        color: white;
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        cursor: pointer;
    }

    #savePhoto {
        background: var(--ppm-gradient);
        color: white;
    }

   @@media (max-width: 768px) {
        .photo-form-wide {
            padding: 20px;
            border-radius: 0;
        }

        h2 {
            font-size: 20px;
            text-align: center;
        }

        .btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>

<div class="container-fluid mt-4 px-0">
    <div class="photo-form-wide">
        <div id="photoUploadArea"></div>

        <div class="py-5">
            <h1 class="text-center page-title" id="title"></h1>
            <div id="list"></div>
        </div>
    </div>
</div>

@Html.Partial("_ProductPhotoModal")

@section scripts {
    <script>
        $(document).ready(function () {
            MainGroupList();
        });

        function MainGroupList() {
            $.ajax({
                type: "Get",
                url: "/Products/MainGroupList",
                dataType: 'html',
                beforeSend: function () {
                    $('#list').html("<div align='center'><br/><br/><br/>Yükleniyor...</div>");
                },
                success: function (data) {
                    $("#list").html(data);
                }
            })

            $("#title").text("Ürün Grupları");
        }

        function SubGroupList(MainGroup) {
            $.ajax({
                type: "Get",
                url: "/Products/SubGroupList?MainGroup=" + MainGroup,
                dataType: 'html',
                beforeSend: function () {
                    $('#list').html("<div align='center'><br/><br/><br/>Yükleniyor...</div>");
                },
                success: function (data) {
                    $("#list").html(data);
                }
            })

            $("#title").text(MainGroup + " Alt Grupları");
        }

        function ItemList(mainGroup, subGroup) {
            $.ajax({
                type: "Get",
                url: "/Products/ItemList?MainGroup=" + mainGroup + "&SubGroup=" + subGroup,
                dataType: 'html',
                beforeSend: function () {
                    $('#list').html("<div align='center'><br/><br/><br/>Yükleniyor...</div>");
                },
                success: function (data) {
                    $("#list").html(data);
                }
            });

            $("#title").text(subGroup + " / Ürünler");
        }

        function showPhotoUploadArea(itemId, itemName, mainGroup, subGroup, itemCode, itemBarcode) {
            const area = $("#photoUploadArea");

            const escapedMainGroup = mainGroup.replace(/'/g, "\\'");
            const escapedSubGroup = subGroup.replace(/'/g, "\\'");

            area.html(`
                <div class="card mb-3 shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title">${itemName} - Fotoğraf Yükle</h5>
                        <p class="text-muted mb-0">Ürün Kodu: ${itemCode || 'N/A'}</p>
                        <p class="text-muted">Barkod: ${itemBarcode || 'N/A'}</p>

                        <div class="mb-3">
                            <input type="file" id="photoFiles" class="form-control" accept="image/*" multiple
                                   onchange="previewPhotos()" />
                        </div>

                        <div id="photoPreviews" class="row mb-3"></div>

                        <div class="d-flex">
                            <button class="btn btn-primary" id="savePhoto"
                                onclick="uploadPhotos(${itemId}, '${escapedMainGroup}', '${escapedSubGroup}')">
                                <i class="fas fa-upload"></i> Fotoğrafları Kaydet
                            </button>
                            <button class="btn btn-secondary ms-2" onclick="clearUploadArea()">
                                İptal
                            </button>
                        </div>

                        <div class="progress mt-3 d-none" id="uploadProgress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            `);
        }

        function clearUploadArea() {
            $("#photoUploadArea").empty();
        }

        function previewPhotos() {
            const files = $("#photoFiles")[0].files;
            const previewContainer = $("#photoPreviews");
            previewContainer.empty();

            if (files.length === 0) return;

            previewContainer.append(`
                <div class="col-12 mb-2">
                    <small class="text-muted">${files.length} fotoğraf seçildi</small>
                </div>
            `);

            const maxPreviews = 10; // önizleme 10 fotoğraf
            for (let i = 0; i < Math.min(files.length, maxPreviews); i++) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    previewContainer.append(`
                        <div class="col-md-2 col-4 mb-2">
                            <img src="${e.target.result}" class="img-thumbnail" style="height: 100px; object-fit: cover;">
                            <small class="d-block text-center">${files[i].name}</small>
                        </div>
                    `);
                };
                reader.readAsDataURL(files[i]);
            }

            if (files.length > maxPreviews) {
                previewContainer.append(`
                    <div class="col-12">
                        <small class="text-muted">+ ${files.length - maxPreviews} daha fazla</small>
                    </div>
                `);
            }
        }

        function uploadPhotos(itemId, maingroup, subgroup) {
            const files = $("#photoFiles")[0].files;
            if (files.length === 0) {
                alert("Lütfen en az bir fotoğraf seçin.");
                return;
            }

            const formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append("photos", files[i]);
            }
            formData.append("itemId", itemId);

            const progressBar = $("#uploadProgress .progress-bar");
            const progressContainer = $("#uploadProgress");
            progressContainer.removeClass("d-none");

            $.ajax({
                url: '/Products/UploadPhotos',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function () {
                    const xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener("progress", function (evt) {
                        if (evt.lengthComputable) {
                            const percentComplete = (evt.loaded / evt.total) * 100;
                            progressBar.css("width", percentComplete + "%");
                        }
                    }, false);
                    return xhr;
                },
                success: function (result) {
                    if (result.success == true) {
                        progressBar.css("width", "100%");
                        setTimeout(() => {
                            alert(result.message + " Fotoğraf başarıyla kaydedildi.");
                            clearUploadArea();
                            // Ürün listesini yenile
                            ItemList(maingroup, subgroup);
                        }, 500);
                    }
                    else {
                        progressBar.css("width", "0%");
                        progressContainer.addClass("bg-danger");
                        alert("Fotoğraflar yüklenirken bir hata oluştu: " + result.message);
                    }
                },
                error: function (xhr, status, error) {
                    console.error(error);
                    progressBar.css("width", "0%");
                    progressContainer.addClass("bg-danger");
                    alert("Fotoğraflar yüklenirken bir hata oluştu: " + error);
                }
            });
        }

        function deleteItemPhoto(itemId) {
            if (!confirm("Bu fotoğrafı silmek istediğinize emin misiniz?")) return;

            $.ajax({
                url: "/Products/DeleteItemPhoto",
                type: "POST",
                data: { id: itemId },
                success: function (response) {
                    if (response.success) {
                        alert("Fotoğraf silindi.");
                        // Sayfayı yenile
                        location.reload();
                    } else {
                        alert("Silme başarısız: " + response.message);
                    }
                },
                error: function (xhr, status, error) {
                    alert("Silme sırasında hata oluştu: " + error);
                }
            });
        }

        function filterItems() {
            const input = document.getElementById('searchInput');
            if (!input) return;

            const filter = input.value.toLocaleLowerCase('tr-TR');
            const container = document.getElementById("itemList");
            if (!container) return;

            const itemCards = container.querySelectorAll('[id^="item-"]');

            itemCards.forEach(card => {
                const title = card.querySelector(".card-title");
                const barcode = card.querySelector(".card-text");

                const nameValue = title ? title.textContent.toLocaleLowerCase('tr-TR') : '';
                const barcodeValue = barcode ? barcode.textContent.toLowerCase() : '';

                if (nameValue.includes(filter) || barcodeValue.includes(filter)) {
                    card.style.display = "block";
                } else {
                    card.style.display = "none";
                }
            });
        }
    </script>
}
