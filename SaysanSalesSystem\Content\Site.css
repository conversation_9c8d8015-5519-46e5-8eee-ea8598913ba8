/* Site.css - <PERSON><PERSON> */

:root {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --dark-color: #5a5c69;
    --card-bg-color: #ffffff;
}

body {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fc;
    padding: 0;
    margin: 0;
}

/* Dashboard Cards */
.dashboard-card {
    border-radius: 12px;
    box-shadow: 0 0.5rem 1.5rem 0 rgba(58, 59, 69, 0.2);
    margin-bottom: 30px;
    transition: all 0.3s ease-in-out;
    height: 100%;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    background-color: var(--card-bg-color);
    border-left: 5px solid var(--primary-color);
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.8rem 2.5rem 0 rgba(58, 59, 69, 0.3);
}

.dashboard-card .card-body {
    padding: 30px 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    min-height: 200px;
}

.dashboard-card .icon-container {
    font-size: 3rem;
    margin-bottom: 15px;
    text-align: center;
    color: var(--primary-color);
}

.dashboard-card .card-title {
    font-size: 1.5rem;
    margin-bottom: 10px;
    font-weight: 700;
    text-align: center;
    color: #444;
}

.dashboard-card .card-description {
    text-align: center;
    color: #666;
}

.dashboard-header {
    margin-bottom: 40px;
}

.dashboard-header h1 {
    font-weight: 700;
    color: #444;
    margin-bottom: 15px;
}

.dashboard-header p {
    color: #666;
    font-size: 1.1rem;
}

/* Content Wrapper */
.content-wrapper {
    padding: 30px;
    min-height: 100vh;
    width: 100%;
}

/* Navbar */
.navbar {
    background-color: white;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

/* Card Colors */
.sales-card { border-left-color: var(--primary-color); }
.sales-card .icon-container { color: var(--primary-color); }

.customers-card { border-left-color: var(--success-color); }
.customers-card .icon-container { color: var(--success-color); }

.products-card { border-left-color: var(--info-color); }
.products-card .icon-container { color: var(--info-color); }

.stock-card { border-left-color: var(--warning-color); }
.stock-card .icon-container { color: var(--warning-color); }

.reports-card { border-left-color: var(--danger-color); }
.reports-card .icon-container { color: var(--danger-color); }

.invoices-card { border-left-color: var(--secondary-color); }
.invoices-card .icon-container { color: var(--secondary-color); }

.settings-card { border-left-color: var(--dark-color); }
.settings-card .icon-container { color: var(--dark-color); }

.support-card { border-left-color: #2c9faf; }
.support-card .icon-container { color: #2c9faf; }

/* Card spacing */
.card-container {
    padding: 0 10px;
}

.logout-btn {
    /* Boyutlar sabit ve eşit olduğu için tam daire oluyor */
    width: 50px;
    height: 50px;
    /* Kırmızı degrade */
    background: linear-gradient(45deg, #dc3545, #c82333);
    /* Ortalanmış icon için flex */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    /* İçerik rengi (ikon) */
    color: #fff;
    font-size: 1.2rem; /* ikon büyüklüğü */
    line-height: 1;
    /* Çerçeve ve gölge */
    border: none;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    /* Geçişler */
    transition: transform 0.2s ease, box-shadow 0.2s ease, background 0.2s ease;
}

    .logout-btn:hover {
        /* Hover’da biraz daha koyu degrade */
        background: linear-gradient(45deg, #c82333, #a71e2a);
        /* Yukarı hafif kaldır */
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    }

/* Responsive */
@media (max-width: 992px) {
    .content-wrapper {
        padding: 20px 15px;
    }
} 