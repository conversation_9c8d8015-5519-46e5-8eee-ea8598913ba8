﻿@{
    ViewBag.Title = "<PERSON><PERSON><PERSON> Fatura";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
 
    <style>
        html, body {
            height: 100%;
            margin: 0;
            background: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .invoice-form-wide {
            width: 100%;
            margin: 0;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 32px;
            min-height: 100vh;
        }

        .custom-table-frame {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: none;
            background: #ffffff;
            padding: 24px;
        }

        h2 {
            color: #212529;
            font-weight: 600;
            font-size: 24px;
            margin-bottom: 32px;
        }

        h5 {
            color: #495057;
            font-weight: 500;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
            font-size: 14px;
        }

        .form-control, .modern-select {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 10px 12px;
            font-size: 14px;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            background: #ffffff;
        }

        .form-control:focus, .modern-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
        }

         

        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2563eb;
            color: white;
        }

        .btn-success {
            background-color: #10b981;
            color: white;
        }

        .btn-success:hover {
            background-color: #059669;
            color: white;
        }

        .btn-outline-secondary {
            background-color: #f9fafb;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline-secondary:hover {
            background-color: #f3f4f6;
            color: #374151;
            border-color: #9ca3af;
        }

        .table {
            margin: 0;
            font-size: 14px;
        }

        .table thead th {
            background-color: #f8fafc;
            color: #374151;
            font-weight: 500;
            border-bottom: 1px solid #e5e7eb;
            padding: 12px;
            font-size: 13px;
        }

        .table tbody tr:hover {
            background-color: #f9fafb;
        }

        .table td {
            padding: 12px;
            border-color: #f1f5f9;
            vertical-align: middle;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: #fafbfc;
        }

        hr {
            border: none;
            height: 1px;
            background-color: #e5e7eb;
            margin: 24px 0;
        }

        .input-group .form-control {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .btn-outline-secondary {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: none;
        }

        /* Responsive */
        @@media (max-width: 768px) {
            .invoice-form-wide {
                padding: 20px;
                border-radius: 0;
            }

            h2 {
                font-size: 20px;
                text-align: center;
            }

            .col-md-3, .col-md-12 {
                margin-bottom: 16px;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .d-flex.justify-content-end {
                justify-content: stretch !important;
            }

            .table-responsive {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
            }
        }

        @@media (max-width: 576px) {
            .invoice-form-wide {
                margin: 0;
                padding: 16px;
                min-height: 100vh;
            }

            .custom-table-frame {
                padding: 16px;
            }

            .table thead th,
            .table td {
                padding: 8px;
                font-size: 12px;
            }

        }
        .table-container {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            margin: 1rem 0;
            padding: 1rem;
        }
        .input-with-numpad input {
            border-radius: 8px;
            border: 1px solid #e1e5e9;
            transition: all 0.3s ease;
        }

            .input-with-numpad input:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }

        .numpad-trigger {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.5rem;
            transition: all 0.3s ease;
        }
        .modern-btn {
            border-radius: 12px;
            padding: 0.8rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

            .modern-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }
        .btn-primary-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
 
    <div class="container-fluid mt-4 px-0">
        <div class="invoice-form-wide">
            
            <form id="invoiceForm">

                <input name="InvoiceType" id="InvoiceType" hidden value="2" /> @*Satış Siparişi Oluştur Invoice Type*@
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="Clients" class="form-label">Müşteri</label>
                        @Html.DropDownList("Clients", (IEnumerable<SelectListItem>)ViewBag.Clients, "Cari Seçiniz", new { @class = "form-control modern-select", @required = "required" })
                    </div>
                    <div class="col-md-3">
                        <label for="InvoiceNumber" class="form-label">Fatura No</label>
                        <input type="text" value="@ViewBag.FicheNo" id="InvoiceNumber" name="InvoiceNumber" class="form-control" placeholder="Fatura Numarası" readonly required>
                    </div>
                    <div class="col-md-3">
                        <label for="SalesConsultantId" class="form-label">Satış Elemanı</label>
                        @Html.DropDownList("SalesConsultantId", (IEnumerable<SelectListItem>)ViewBag.SalesConsultants, "Satış Elemanı Seçiniz", new { @class = "form-control modern-select", @required = "required" })
                    </div>
                    <div class="col-md-3">
                        <label for="Salesman" class="form-label">Hazırlayan</label>
                        @Html.DropDownList("Salesman", (IEnumerable<SelectListItem>)ViewBag.SalesConsultants, "Hazırlayan Eleman Seçiniz", new { @class = "form-control modern-select", @required = "required" })
                    </div>
                    <div class="col-md-3">
                        <label for="Payment" class="form-label">Tahsilat Şekli</label>
                        @Html.DropDownList("Payment", (IEnumerable<SelectListItem>)ViewBag.Payment, "Tahsilat Şekli Seçiniz", new { @class = "form-control modern-select", @required = "required" })
                    </div>
                    <div class="col-md-6">
                        <label for="Barcode" class="form-label">Barkod</label>
                        <div class="input-group">
                            <input type="text" id="Barcode" name="Barcode" class="form-control barcode-input"
                                   placeholder="Barkod okutun veya manuel girin...">
                            <button type="button" class="btn btn-primary-modern" id="scanBarcodeBtn">
                                <i class="fa fa-barcode"></i>
                            </button>
                        </div>
                    </div>

                    <div class="col-md-3 align-items-end">
                        <div>
                            <label class="form-label invisible">Boş</label>
                            <button type="button" class="modern-btn btn-primary-modern w-100" onclick="showProductModal()">
                                <i class="fa fa-plus me-2"></i> Ürün Seç
                            </button>
                        </div>
                    </div>

                </div>

                <hr />
                <div class="table-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">

                        <span class="badge btn-primary-modern" id="productCount">0 Ürün</span>
                    </div>

                    <div class="table-responsive">
                        <table class="table modern-table" id="productsTable">
                            <thead>
                                <tr>
                                    <th>Ürün Adı</th>
                                    <th>Barkod</th>
                                    <th>Adet</th>
                                    <th>Ürün Kodu</th>
                                    <th>Birim Fiyat</th>
                                    <th class="text-center">İşlem</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4" class="text-end"><strong>Toplam Tutar:</strong></td>
                                    <td id="totalPrice"><strong>0.00 ₺</strong></td>
                                    <td></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="d-flex justify-content-end mt-3 gap-2">
                    <button type="button" class="btn btn-secondary" id="clearFormBtn" onclick="clearForm()">Temizle</button>
                    <button type="button" class="btn btn-success" id="saveInvoiceBtn">Kaydet</button>
                </div>
            </form>
        </div>
    </div>


    @section scripts {
        <script>
            $(document).ready(function () {
                updateProductCount();
                $('#psm-confirm').on('click', function () {
                    var selections = ProductSelector.getSelections();
                    var logicalRefs = selections.map(function (item) {
                        return item.LogicalRef;
                    });

                    $.ajax({
                        url: '/Invoice/GetSelectedItems', // ← sunucudaki endpoint burası olacak
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ logicalRefs: logicalRefs }),
                        success: function (response) {
                            if (response.success) {

                                addMultipleProductsToTable(response.data);
                            } else {
                                alert("Ürünler getirilemedi.");
                            }
                        },
                        error: function (xhr, status, error) {
                            console.error('Ürünleri çekerken hata:', error);
                        }
                    });
                });
            });

            function updateProductCount() {
                const count = document.querySelectorAll('#productsTable tbody tr').length;
                document.getElementById('productCount').textContent = count + ' Ürün';
            }

            // Ürünler tabloya ekleniyor mu kontrolü için
            function findProductRow(barcode) {
                const rows = document.querySelectorAll('#productsTable tbody tr');
                for (let row of rows) {
                    if (row.getAttribute('data-barcode') === barcode) {
                        return row;
                    }
                }
                return null;
            }

            // Tabloya ürün ekle
            function addProductToTable(product, qty = 1) {
                let row = findProductRow(product.barcode);
                if (row) {
                    // Varsa adeti artır
                    const qtyInput = row.querySelector(`#qty-${product.id}`);
                    qtyInput.value = parseInt(qtyInput.value) + qty;
                } else {
                    // Yoksa yeni satır ekle
                    const tbody = document.querySelector('#productsTable tbody');
                    row = document.createElement('tr');
                    row.setAttribute('data-barcode', product.barcode);
                    row.setAttribute('data-product-id', product.id);
                    row.setAttribute('data-product-logRef', product.logicalRef);
                    row.innerHTML = `
                       <td>${product.name}</td>
                       <td>${product.barcode}</td>
                        <td>
    <div class="input-with-numpad">
        <input value="${qty}" min="1" type="number" class="form-control numpad-input pe-5" id="qty-${product.id}">
        <button type="button" class="numpad-trigger" data-input-target="qty-${product.id}">
            <i class="fa-solid fa-calculator"></i>
        </button>
    </div>
</td>
                       <td><span class="badge bg-secondary">${product.code}</span></td>
                       <td><strong>${product.listPrice} ₺</strong></td>
                       <td class="text-center">
                           <button type="button" class="btn remove-btn btn-sm remove-product">
                               <i class="fa fa-trash"></i>
                           </button>
                       </td>
                   `;
                    // Miktar değişirse toplam güncellensin
                    const qtyInput = row.querySelector(`#qty-${product.id}`);
                    qtyInput.addEventListener('input', calculateTotalPrice);

                    // Sil butonu
                    row.querySelector('.remove-product').onclick = function () {
                        row.remove();
                        calculateTotalPrice();
                        updateProductCount();
                    };
                    tbody.appendChild(row);
                }
                calculateTotalPrice();
                updateProductCount();
            }
            //Tabloya çoklu ürün ekle
            function addMultipleProductsToTable(products) {
                if (!Array.isArray(products)) return;

                products.forEach(product => {
                    addProductToTable({
                        id: product.Id,
                        barcode: product.Barcode,
                        name: product.Name,
                        code: product.Code,
                        listPrice: product.ListPrice,
                        logicalRef: product.LogicalRef
                    });
                });
            }
            function calculateTotalPrice() {
                let total = 0;
                const rows = document.querySelectorAll('#productsTable tbody tr');

                rows.forEach(row => {
                    const barcode = row.getAttribute('data-barcode');
                    const qtyInput = row.querySelector('input[type="number"]');
                    const priceCell = row.children[4]; // 5. sütun: fiyat

                    const qty = parseFloat(qtyInput.value) || 0;
                    const unitPrice = parseFloat(priceCell.textContent) || 0;

                    total += qty * unitPrice;
                });

                // Toplamı güncelle
                document.getElementById('totalPrice').textContent = total.toFixed(4);
            }

            // Barkoddan ürün ekleme
            document.getElementById('Barcode').addEventListener('keydown', function (e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const barcode = this.value.trim();
                    if (!barcode) return;
                    // AJAX ile sunucudan ürün çek
                    fetch(`/Invoice/FindProductByBarcode?barcode=${encodeURIComponent(barcode)}`)
                        .then(response => response.json())
                        .then(result => {
                            if (result.success && result.data) {
                                 
                                const product = {
                                    id: result.data.Id,
                                    name: result.data.Name,
                                    code: result.data.Code,
                                    listPrice: result.data.ListPrice,
                                    barcode: result.data.Barcode,
                                    logicalRef: result.data.LogicalRef
                                };
                                addProductToTable(product);
                                document.getElementById('Barcode').value = '';
                            } else {
                                alert('Barkod ile eşleşen ürün bulunamadı!');
                            }
                        })
                        .catch(() => {
                            alert('Sunucuya bağlanırken hata oluştu!');
                        })
                        .finally(() => {
                            // Her durumda input temizlenir
                            document.getElementById('Barcode').value = '';
                        });
                }
            });
            // Barkod butonu (örnek: barkod alanına odaklan)
            document.getElementById('scanBarcodeBtn').addEventListener('click', function () {
                document.getElementById('Barcode').focus();
            });

            // Kaydet butonu - controller'a istek gönder
            document.getElementById('saveInvoiceBtn').addEventListener('click', function () {
                // Form verilerini topla
                const formData = {
                    SalesConsultantId: document.querySelector('select[name="SalesConsultantId"]').value,
                    InvoiceNumber: document.getElementById('InvoiceNumber').value,
                    InvoiceType: document.getElementById('InvoiceType').value,
                    ClientId: document.querySelector('select[name="Clients"]').value,
                    Salesman: document.querySelector('select[name="Salesman"]').value,
                    Payment: document.querySelector('select[name="Payment"]').value,
                    Products: []
                };
                if (!formData.SalesConsultantId) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Eksik Bilgi!',
                        text: 'Lütfen Satış Elemanı seçiniz.'
                    });
                    return;
                }
                if (!formData.InvoiceNumber) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Eksik Bilgi!',
                        text: 'Lütfen Fatura Numarası giriniz.'
                    });
                    return;
                }
                if (!formData.InvoiceType) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Eksik Bilgi!',
                        text: 'Lütfen Fatura Tipi seçiniz.'
                    });
                    return;
                }
                if (!formData.ClientId) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Eksik Bilgi!',
                        text: 'Lütfen Müşteri seçiniz.'
                    });
                    return;
                }
                if (!formData.Salesman) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Eksik Bilgi!',
                        text: 'Lütfen Hazırlayan kişi seçiniz.'
                    });
                    return;
                }
                if (!formData.Payment) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Eksik Bilgi!',
                        text: 'Lütfen Tahsilat Tipi seçiniz.'
                    });
                    return;
                }

                // Ürün bilgilerini topla
                const tableRows = document.querySelectorAll('#productsTable tbody tr');
                tableRows.forEach(row => {
                    const productData = {
                        ProductId: row.getAttribute('data-product-id'),
                        LogicalRef: row.getAttribute('data-product-logRef'),
                        Name: row.cells[0].textContent,
                        Barcode: row.cells[1].textContent,
                        Quantity: row.querySelector('input[type="number"]').value,
                        Code: row.cells[3].textContent,
                        Price: row.cells[4].textContent
                    };
                    formData.Products.push(productData);
                });
                Swal.fire({
                    title: 'Kaydediliyor...',
                    text: 'Lütfen bekleyin',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                // AJAX ile controller'a gönder
                fetch('/Invoice/SaveInvoice', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Başarılı!',
                                text: 'Sipariş başarıyla kaydedildi!',
                                timer: 2000,
                                showConfirmButton: false
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Hata!',
                                text: 'Kaydetme sırasında hata oluştu: ' + data.message
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Kaydetme sırasında hata oluştu.');
                    });
            });
            document.getElementById('productsTable').addEventListener('focusin', function (e) {
                if (e.target.classList.contains('numpad-input')) {
                    const inputId = e.target.id;
                    const button = document.querySelector(`.numpad-trigger[data-input-target="${inputId}"]`);
                    if (button) {
                        button.click(); // NumPad butonunu tetikle
                    }
                }
            });

            function clearForm() {
                // Select alanlarını sıfırla
                resetDropdown('SalesConsultantId');
                resetDropdown('Clients');
                resetDropdown('Salesman');
                resetDropdown('Payment');


                // Ürünler tablosunu sıfırla
                const productsTableBody = document.querySelector('#productsTable tbody');
                if (productsTableBody) {
                    productsTableBody.innerHTML = '';
                }

                // Ürün sayısını sıfırla
                const productCountEl = document.getElementById('productCount');
                if (productCountEl) {
                    productCountEl.textContent = '0 Ürün';
                }

                // Toplam fiyatı sıfırla
                const totalPriceEl = document.getElementById('totalPrice');
                if (totalPriceEl) {
                    totalPriceEl.innerHTML = '<strong>0.00 ₺</strong>';
                }
            }
            function resetDropdown(id) {
                const $el = $('#' + id);

                // Temel sıfırlama
                $el.val('').prop('selectedIndex', 0).trigger('change');

                // Eğer select2 kullanılıyorsa:
                if ($el.data('select2')) {
                    $el.val('').trigger('change.select2');
                }

                // Eğer bootstrap-select kullanılıyorsa:
                if ($el.hasClass('selectpicker')) {
                    $el.selectpicker('val', '');
                }
            }
        </script>

    }

