//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace SaysanSalesSystem.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class Invoice
    {
        public int Id { get; set; }
        public Nullable<int> InvoiceType { get; set; }
        public string InvoiceNumber { get; set; }
        public Nullable<int> CustomerId { get; set; }
        public Nullable<int> WarehouseNumber { get; set; }
        public Nullable<int> PaymentRef { get; set; }
        public Nullable<int> BranchNumber { get; set; }
        public Nullable<System.DateTime> Date { get; set; }
        public string CreatedBy { get; set; }
        public Nullable<int> SalesmanRef { get; set; }
        public Nullable<int> PreparedRef { get; set; }
        public Nullable<short> DocumentType { get; set; }
        public Nullable<System.DateTime> CreateDate { get; set; }
        public Nullable<int> IsTransfer { get; set; }
        public Nullable<bool> IsDeleted { get; set; }
    }
}
